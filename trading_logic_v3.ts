import { CandleChartResult } from 'binance-api-node'

// <PERSON><PERSON>nh nghĩa interface cho tín hiệu giao dịch
export interface TradingSignal {
  positionSide: 'LONG' | 'SHORT' | null
  entryPrice: number | null
  takeProfitPrice: number | null
  stopLossPrice: number | null
}

// Hàm tính Moving Average
export function calculateMA(prices: number[], period: number): number {
  if (prices.length < period) return NaN
  const sum = prices.slice(-period).reduce((acc, price) => acc + price, 0)
  return sum / period
}

// Hàm tạo tín hiệu giao dịch
export async function generateTradingSignal(candles: CandleChartResult[]): Promise<TradingSignal> {
  // Tham số cho Moving Averages
  const shortMAPeriod = 10 // MA ngắn hạn: 10 phút
  const longMAPeriod = 50 // MA dài hạn: 50 phút

  // Kiểm tra dữ liệu đầu vào
  if (!candles || candles.length < longMAPeriod) {
    console.log('Không đủ dữ liệu nến để tạo tín hiệu giao dịch.')
    return { positionSide: null, entryPrice: null, takeProfitPrice: null, stopLossPrice: null }
  }

  // Lấy giá đóng cửa từ dữ liệu nến
  const closePrices = candles.map(candle => parseFloat(candle.close))

  // Tính MA hiện tại và MA trước đó
  const shortMA = calculateMA(closePrices, shortMAPeriod)
  const longMA = calculateMA(closePrices, longMAPeriod)
  const prevShortMA = calculateMA(closePrices.slice(0, -1), shortMAPeriod)
  const prevLongMA = calculateMA(closePrices.slice(0, -1), longMAPeriod)

  // Lấy thông tin nến cuối và nến trước đó
  const lastCandle = candles[candles.length - 1]
  const prevCandle = candles[candles.length - 2]

  // Kiểm tra tín hiệu Long
  if (prevShortMA < prevLongMA && shortMA > longMA) {
    const entryPrice = parseFloat(lastCandle.close)
    const stopLossPrice = parseFloat(prevCandle.low)
    const takeProfitPrice = entryPrice + 2 * (entryPrice - stopLossPrice) // Risk/Reward 1:2

    console.log(`🍖 Trading signal: `, {
      positionSide: 'LONG',
      entryPrice,
      takeProfitPrice,
      stopLossPrice,
    })

    return {
      positionSide: 'LONG',
      entryPrice,
      takeProfitPrice,
      stopLossPrice,
    }
  }
  // Kiểm tra tín hiệu Short
  else if (prevShortMA > prevLongMA && shortMA < longMA) {
    const entryPrice = parseFloat(lastCandle.close)
    const stopLossPrice = parseFloat(prevCandle.high)
    const takeProfitPrice = entryPrice - 2 * (stopLossPrice - entryPrice) // Risk/Reward 1:2

    console.log(`🍖 Trading signal: `, {
      positionSide: 'SHORT',
      entryPrice,
      takeProfitPrice,
      stopLossPrice,
    })

    return {
      positionSide: 'SHORT',
      entryPrice,
      takeProfitPrice,
      stopLossPrice,
    }
  }

  // Không có tín hiệu
  return { positionSide: null, entryPrice: null, takeProfitPrice: null, stopLossPrice: null }
}
