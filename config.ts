// Load environment variables
import 'dotenv/config' // Use import for dotenv in TypeScript

interface ApiKeys {
  apiKey: string
  apiSecret: string
  httpFutures: string
  wsFutures: string
}

/**
 * Function to get API keys and endpoints from environment variables
 * @returns {ApiKeys} - An object containing API keys and endpoints
 * @throws {Error} - If API_KEY, API_SECRET, httpFutures, or wsFutures are missing in .env file
 */
export function loadApiKeys(): ApiKeys {
  const apiKey: string | undefined = process.env.API_KEY
  const apiSecret: string | undefined = process.env.API_SECRET
  const httpFutures: string | undefined = process.env.httpFutures
  const wsFutures: string | undefined = process.env.wsFutures

  if (!apiKey || !apiSecret || !httpFutures || !wsFutures) {
    throw new Error('Missing API_KEY, API_SECRET, httpFutures, or wsFutures in .env file')
  }

  return {
    apiKey,
    apiSecret,
    wsFutures,
    httpFutures,
  } as ApiKeys // Type assertion to ApiKeys interface
}
