import { CandleChartResult } from 'binance-api-node'
import { TradingSignal } from './main/utils/trade_model'

// Calculate EMA
function calculateEMA(prices: number[], period: number): number {
  if (prices.length < period) return NaN
  const k = 2 / (period + 1)
  let ema = prices[0]
  for (let i = 1; i < prices.length; i++) {
    ema = prices[i] * k + ema * (1 - k)
  }
  return ema
}

// Calculate RSI
function calculateRSI(prices: number[], period: number): number {
  if (prices.length < period) return NaN
  const windowPrices = prices.slice(-period)
  let gains = 0
  let losses = 0
  for (let i = 1; i < windowPrices.length; i++) {
    const change = windowPrices[i] - windowPrices[i - 1]
    if (change > 0) gains += change
    else losses -= change
  }
  const avgGain = gains / (period - 1)
  const avgLoss = losses / (period - 1)
  if (avgLoss === 0) return 100
  const rs = avgGain / avgLoss
  return 100 - 100 / (1 + rs)
}

// Calculate ATR
function calculateATR(candles: CandleChartResult[], period: number): number {
  if (candles.length < period) return NaN
  let trSum = 0
  for (let i = 1; i < period; i++) {
    const high = parseFloat(candles[i].high)
    const low = parseFloat(candles[i].low)
    const prevClose = parseFloat(candles[i - 1].close)
    const tr = Math.max(high - low, Math.abs(high - prevClose), Math.abs(low - prevClose))
    trSum += tr
  }
  return trSum / (period - 1)
}

// Calculate Bollinger Bands
function calculateBollingerBands(
  prices: number[],
  period: number,
  multiplier: number,
): { upper: number; middle: number; lower: number } {
  // Calculate SMA
  const sma = prices.slice(-period).reduce((sum, price) => sum + price, 0) / period

  // Calculate standard deviation
  const squaredDifferences = prices.slice(-period).map(price => Math.pow(price - sma, 2))
  const variance = squaredDifferences.reduce((sum, val) => sum + val, 0) / period
  const stdDev = Math.sqrt(variance)

  // Calculate bands
  const upper = sma + multiplier * stdDev
  const lower = sma - multiplier * stdDev

  return { upper, middle: sma, lower }
}

// Check for volume spike
function isVolumeSpikeUp(
  candles: CandleChartResult[],
  period: number,
  multiplier: number,
): boolean {
  if (candles.length < period + 1) return false

  const volumes = candles.slice(-period - 1, -1).map(c => parseFloat(c.volume))
  const avgVolume = volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length
  const currentVolume = parseFloat(candles[candles.length - 1].volume)

  return currentVolume > avgVolume * multiplier
}

// Generate trading signal
export function generateTradingSignal(windowCandles: CandleChartResult[]): TradingSignal {
  // Optimized parameters
  const emaShortPeriod = 8
  const emaLongPeriod = 21
  const rsiPeriod = 14
  const atrPeriod = 14
  const bbPeriod = 20
  const bbMultiplier = 2.0
  const volumePeriod = 10
  const volumeMultiplier = 1.5

  // Check if we have enough data
  const requiredPeriod = Math.max(emaLongPeriod + 1, rsiPeriod, atrPeriod, bbPeriod)
  if (windowCandles.length < requiredPeriod) {
    return { positionSide: null, entryPrice: null, stopLossPrice: null, takeProfitPrice: null }
  }

  // Get closing prices
  const closePrices = windowCandles.map(candle => parseFloat(candle.close))

  // Get recent candles for EMA calculation
  const recentCandles = windowCandles.slice(-(emaLongPeriod + 1))
  const recentClose = recentCandles.map(c => parseFloat(c.close))

  // Calculate EMAs
  const emaShort = calculateEMA(recentClose, emaShortPeriod)
  const emaLong = calculateEMA(recentClose, emaLongPeriod)
  const prevClose = recentClose.slice(0, -1)
  const prevEmaShort = calculateEMA(prevClose, emaShortPeriod)
  const prevEmaLong = calculateEMA(prevClose, emaLongPeriod)

  // Calculate RSI
  const rsi = calculateRSI(closePrices, rsiPeriod)

  // Calculate Bollinger Bands
  const bb = calculateBollingerBands(closePrices, bbPeriod, bbMultiplier)

  // Check for volume spike
  const hasVolumeSpikeUp = isVolumeSpikeUp(windowCandles, volumePeriod, volumeMultiplier)

  // Calculate ATR
  const atr = calculateATR(windowCandles, atrPeriod)

  // Entry price
  const entryPrice = parseFloat(windowCandles[windowCandles.length - 1].close)
  const currentPrice = entryPrice

  // LONG signal conditions
  if (
    prevEmaShort < prevEmaLong &&
    emaShort > emaLong &&
    rsi < 40 &&
    currentPrice < bb.lower * 1.05
    // Volume spike is optional for LONG
  ) {
    // Calculate stop loss and take profit
    const stopLossPrice = entryPrice - 0.2 * atr
    const takeProfitPrice = entryPrice + 1.0 * atr // Risk:Reward = 1:5

    console.log(
      `🧪 Trading signal LONG at price ${entryPrice.toFixed(
        4,
      )} with stop loss ${stopLossPrice.toFixed(4)} and take profit ${takeProfitPrice.toFixed(
        4,
      )} (RSI: ${rsi.toFixed(2)})`,
    )

    return {
      positionSide: 'LONG',
      entryPrice,
      stopLossPrice,
      takeProfitPrice,
    }
  }
  // SHORT signal conditions
  else if (
    prevEmaShort > prevEmaLong &&
    emaShort < emaLong &&
    rsi > 60 &&
    currentPrice > bb.upper * 0.95
    // Volume spike is now optional for SHORT
  ) {
    // Calculate stop loss and take profit
    const stopLossPrice = entryPrice + 0.2 * atr
    const takeProfitPrice = entryPrice - 1.0 * atr // Risk:Reward = 1:5

    console.log(
      `🌡️ Trading signal SHORT at price ${entryPrice.toFixed(
        4,
      )} with stop loss ${stopLossPrice.toFixed(4)} and take profit ${takeProfitPrice.toFixed(
        4,
      )} (RSI: ${rsi.toFixed(2)})`,
    )

    return {
      positionSide: 'SHORT',
      entryPrice,
      stopLossPrice,
      takeProfitPrice,
    }
  }

  // No signal
  return { positionSide: null, entryPrice: null, stopLossPrice: null, takeProfitPrice: null }
}
