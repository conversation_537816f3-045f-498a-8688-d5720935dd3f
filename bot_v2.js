// @ts-nocheck

const Binance = require('binance-api-node').default
const { setIntervalAsync } = require('set-interval-async/dynamic')
const { loadApiKeys } = require('./config')
const { OrderSide, OrderType } = require('binance-api-node')
const BalanceLogger = require('./main/log/balance_logger')

const logger = new BalanceLogger('./main/log/balance_log.csv')
const { apiKey, apiSecret, httpFutures, wsFutures } = loadApiKeys()

const client = Binance({
  apiKey,
  apiSecret,
  httpFutures,
  wsFutures,
})

// <PERSON><PERSON><PERSON> thông số giao dịch
const SYMBOL = 'BTCUSDT'
const LEVERAGE = 20
const ACCOUNT_PERCENTAGE = 0.2
const STOP_LOSS_PERCENT = 0.04 // 2%
const TAKE_PROFIT_PERCENT = 0.1 // 5%
const RISK_REWARD_RATIO = 1.5 // R:R = 1:1.5
const INTERVAL = '1m'
let positionOpen = false
let currentPosition = null
let positionSide = 'NONE'
let inTrade = false

const emaPeriod = 8 // Period for EMA
const bbPeriod = 20 // Period for Bollinger Bands
const bbStdDevUp = 2 // Standard deviation multiplier for upper outer BB
const bbStdDevDown = 2 // Standard deviation multiplier for lower outer BB
const bbStdDevUpInner = 1 // Standard deviation multiplier for upper inner BB
const bbStdDevDownInner = 1 // Standard deviation multiplier for lower inner BB

function getUSDTBalance(balances) {
  const usdtItem = balances.find(item => item.asset === 'USDT')
  if (usdtItem) {
    return parseFloat(usdtItem.balance) // Convert to number
  } else {
    return NaN // Or handle the case where USDT isn't found
  }
}

async function checkBalance() {
  const balance = await getTotalBalance()
  console.log(`Current Balance: ${balance.toFixed(2)} USDT`)
  logger.logBalance(balance)
}

async function getTotalBalance() {
  const accountInfo = await client.futuresAccountBalance()
  const usdtBalance = getUSDTBalance(accountInfo)

  return usdtBalance
}

// Hàm tính toán kích thước lệnh
async function calculateOrderSize() {
  const usdtBalance = await getTotalBalance()
  const orderValue = usdtBalance * ACCOUNT_PERCENTAGE
  console.log('Order Value: ', orderValue)
  return orderValue
}

async function cancelAllOrders() {
  const openOrders = await client.futuresOpenOrders({ symbol: SYMBOL })
  for (const order of openOrders) {
    await client.futuresCancelOrder({ symbol: SYMBOL, orderId: parseInt(order.orderId) })
  }
  console.log('Đã hủy tất cả các lệnh đang mở.')
}

async function openPosition(signal) {
  if (signal == 'NONE') return

  await cancelAllOrders()

  const orderSize = await calculateOrderSize()
  const price = parseFloat((await client.futuresPrices())[SYMBOL])
  const quantity = ((orderSize * LEVERAGE) / price).toFixed(3)

  console.log('quantity: ', quantity)
  console.log('price: ', price)
  console.log('signal: ', signal)

  if (signal === 'LONG') {
    console.log('** open long position')
    await client.futuresOrder({
      symbol: SYMBOL,
      side: 'BUY',
      type: 'MARKET',
      quantity,
    })
  } else {
    console.log('** open short position')
    await client.futuresOrder({
      symbol: SYMBOL,
      side: 'SELL',
      type: 'MARKET',
      quantity,
    })
  }

  const stopLossAmount = orderSize * STOP_LOSS_PERCENT
  const takeProfitAmount = orderSize * TAKE_PROFIT_PERCENT

  let stopLossPrice =
    signal === 'LONG'
      ? price - stopLossAmount / parseFloat(quantity)
      : price + stopLossAmount / parseFloat(quantity)
  let stopLossPriceFix = stopLossPrice.toFixed(0)

  let takeProfitPrice =
    signal === 'LONG'
      ? price + takeProfitAmount / parseFloat(quantity)
      : price - takeProfitAmount / parseFloat(quantity)
  let takeProfitPriceFix = takeProfitPrice.toFixed(0)

  console.log('** stopLossPrice: ', stopLossPrice)
  await client.futuresOrder({
    symbol: SYMBOL,
    side: signal === 'LONG' ? 'SELL' : 'BUY',
    type: 'STOP',
    price: stopLossPriceFix,
    stopPrice: stopLossPriceFix,
    quantity,
  })
  console.log('** Done stopLossPrice: ', stopLossPrice)

  console.log('** takeProfitPrice: ', takeProfitPrice)
  await client.futuresOrder({
    symbol: SYMBOL,
    side: signal === 'LONG' ? 'SELL' : 'BUY',
    type: 'TAKE_PROFIT',
    price: takeProfitPriceFix,
    stopPrice: takeProfitPriceFix,
    quantity,
  })
  console.log('** Done takeProfitPrice: ', takeProfitPrice)

  positionOpen = true
  currentPosition = signal
}

async function getCurrentPosition() {
  const positions = await client.futuresPositionRisk()
  const position = positions.find(pos => pos.symbol === SYMBOL)
  console.log('position', position)
  if (position && parseFloat(position.positionAmt) != 0) {
    const size = parseFloat(position.positionAmt)
    const entryPrice = parseFloat(position.entryPrice)
    const unrealizedProfit = parseFloat(position.unRealizedProfit)

    console.log(
      `Current Position: ${size}, Entry Price: ${entryPrice}, Unrealized Profit: ${unrealizedProfit}`,
    )

    currentPosition = position
    positionOpen = true
  } else {
    console.log('No Position Open')
    currentPosition = null
    positionOpen = false
    return null
  }
}

function calculateEMA(data, period) {
  if (data.length < period) return null
  let ema = []
  let multiplier = 2 / (period + 1)
  ema[period - 1] = data.slice(0, period).reduce((sum, val) => sum + val, 0) / period // SMA for initial EMA value

  for (let i = period; i < data.length; i++) {
    ema[i] = (data[i] - ema[i - 1]) * multiplier + ema[i - 1]
  }
  return ema.slice(period - 1) // Return EMA values starting from where it's valid
}

function getTrendFromMiddleBB(middleBBValues) {
  if (!middleBBValues || middleBBValues.length < 2) {
    console.log('Insufficient middle Bollinger Band data to determine trend.')
    return 'SIDEWAYS' // Default to sideways if not enough data
  }

  const lookbackPeriod = Math.min(5, middleBBValues.length - 1) // Look back at most 5 periods, or less if data is shorter

  const currentMiddleBB = middleBBValues[middleBBValues.length - 1]
  const previousMiddleBB = middleBBValues[middleBBValues.length - lookbackPeriod]

  const diff = currentMiddleBB - previousMiddleBB

  const thresholdPercentage = 0.0005
  const thresholdValueUp = previousMiddleBB * (1 + thresholdPercentage)
  const thresholdValueDown = previousMiddleBB * (1 - thresholdPercentage)

  if (currentMiddleBB > thresholdValueUp) {
    return 'UPTREND'
  } else if (currentMiddleBB < thresholdValueDown) {
    return 'DOWNTREND'
  } else {
    return 'SIDEWAYS'
  }
}

// Function to calculate Bollinger Bands (same as before)
function calculateBollingerBands(data, period, stdDevUp, stdDevDown) {
  if (data.length < period) return null
  let bb = { upper: [], lower: [], middle: [] }
  for (let i = period - 1; i < data.length; i++) {
    const slice = data.slice(i - period + 1, i + 1)
    const avg = slice.reduce((sum, val) => sum + val, 0) / period
    const stdDev = Math.sqrt(slice.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / period)

    // @ts-ignore
    bb.middle[i] = avg
    // @ts-ignore
    bb.upper[i] = avg + stdDev * stdDevUp
    // @ts-ignore
    bb.lower[i] = avg - stdDev * stdDevDown
  }
  return {
    upper: bb.upper.slice(period - 1),
    lower: bb.lower.slice(period - 1),
    middle: bb.middle.slice(period - 1),
  }
}

// --- Trading Logic Function (Modified to return signal) ---
async function tradingLogic() {
  try {
    const candles = await client.futuresCandles({
      symbol: SYMBOL,
      interval: INTERVAL,
      limit: Math.max(emaPeriod, bbPeriod) + 5,
    }) // Fetch more candles

    if (candles.length <= Math.max(emaPeriod, bbPeriod)) {
      console.log('Not enough candle data to calculate indicators.')
      return 'NONE' // Return 'NONE' if not enough data
    }

    const closePrices = candles.map(candle => parseFloat(candle[4])) // Extract close prices

    // Calculate EMA
    const emaValues = calculateEMA(closePrices, emaPeriod)
    if (!emaValues) {
      console.log('Could not calculate EMA.')
      return 'NONE' // Return 'NONE' if EMA calculation fails
    }
    const currentEMA = emaValues[emaValues.length - 1]

    // Calculate Bollinger Bands (Outer and Inner)
    const outerBB = calculateBollingerBands(closePrices, bbPeriod, bbStdDevUp, bbStdDevDown)
    const innerBB = calculateBollingerBands(
      closePrices,
      bbPeriod,
      bbStdDevUpInner,
      bbStdDevDownInner,
    )

    if (!outerBB || !innerBB) {
      console.log('Could not calculate Bollinger Bands.')
      return 'NONE' // Return 'NONE' if BB calculation fails
    }

    const currentClose = closePrices[closePrices.length - 1]
    const previousClose = closePrices[closePrices.length - 2] // For crossover detection

    const currentUpperOuterBB = outerBB.upper[outerBB.upper.length - 1]
    const currentLowerOuterBB = outerBB.lower[outerBB.lower.length - 1]
    const currentUpperInnerBB = innerBB.upper[innerBB.upper.length - 1]
    const currentLowerInnerBB = innerBB.lower[innerBB.lower.length - 1]

    let signal = 'NONE' // Default signal

    // 1. Check if already in a trade (You might manage position state externally now)
    if (inTrade) {
      // You'll need to manage 'inTrade' and 'positionSide' externally if needed
      // --- Exit Logic Signal (Simplified - you'll need more sophisticated exit strategies) ---

      if (positionSide === 'LONG') {
        // Exit Long: Example - Price falls below EMA
        if (currentClose < currentEMA) {
          console.log('Exit LONG signal - Price below EMA.')
          signal = 'CLOSE_LONG' // Indicate close long position
          positionSide = 'NONE' // Update position state if you are managing it here
          inTrade = false // Update trade state if you are managing it here
        }
      } else if (positionSide === 'SHORT') {
        // Exit Short: Example - Price rises above EMA
        if (currentClose > currentEMA) {
          console.log('Exit SHORT signal - Price above EMA.')
          signal = 'CLOSE_SHORT' // Indicate close short position
          positionSide = 'NONE' // Update position state if you are managing it here
          inTrade = false // Update trade state if you are managing it here
        }
      }
      return signal // Exit function after checking exit conditions
    }

    // 2. Entry Logic (if not in a trade)
    if (!inTrade) {
      // You'll need to manage 'inTrade' externally if needed
      // --- Long Entry Condition ---
      if (
        currentClose > currentEMA &&
        currentClose > currentUpperInnerBB &&
        previousClose <= currentUpperInnerBB &&
        currentClose < currentUpperOuterBB
      ) {
        // Price crosses above EMA and Inner Upper BB
        console.log('LONG Entry Signal - Price above EMA and Inner Upper BB.')
        signal = 'LONG'
        positionSide = 'LONG' // Update position state if you are managing it here
        inTrade = true // Update trade state if you are managing it here
      }

      // --- Short Entry Condition ---
      else if (
        currentClose < currentEMA &&
        currentClose < currentLowerInnerBB &&
        previousClose >= currentLowerInnerBB &&
        currentClose > currentLowerOuterBB
      ) {
        // Price crosses below EMA and Inner Lower BB
        console.log('SHORT Entry Signal - Price below EMA and Inner Lower BB.')
        signal = 'SHORT'
        positionSide = 'SHORT' // Update position state if you are managing it here
        inTrade = true // Update trade state if you are managing it here
      }
    }

    return signal // Return the calculated signal
  } catch (error) {
    console.error('Error in trading logic:', error)
    return 'NONE' // Return 'NONE' in case of error
  }
}

async function runBot() {
  console.log('=====================================')
  await checkBalance()
  await getCurrentPosition()
  if (!positionOpen) {
    const signal = await tradingLogic()
    await openPosition(signal)
  } else {
    // await closePositionCheck();
  }
  console.log('=====================================')
}

;(async function() {
  console.log('=== Bot is running ===')
  console.log('test connect binance', await client.ping())
  await client.futuresLeverage({ symbol: SYMBOL, leverage: LEVERAGE })
  runBot()
  setIntervalAsync(async () => {
    runBot()
  }, 60000)
})()
