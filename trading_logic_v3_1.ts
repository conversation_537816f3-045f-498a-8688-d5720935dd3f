import { CandleChartResult } from 'binance-api-node'

// <PERSON><PERSON><PERSON> nghĩa interface cho tín hiệu giao dịch
export interface TradingSignal {
  positionSide: 'LONG' | 'SHORT' | null
  entryPrice: number | null
  takeProfitPrice: number | null
  stopLossPrice: number | null
}

// Hàm tính EMA (Exponential Moving Average)
export function calculateEMA(prices: number[], period: number): number {
  if (prices.length < period) return NaN
  const k = 2 / (period + 1)
  let ema = prices[0]
  for (let i = 1; i < prices.length; i++) {
    ema = prices[i] * k + ema * (1 - k)
  }
  return ema
}

// Hàm tính MACD
export function calculateMACD(prices: number[]): { macd: number; signal: number } {
  const shortPeriod = 12
  const longPeriod = 26
  const signalPeriod = 9
  if (prices.length < longPeriod + signalPeriod) return { macd: NaN, signal: NaN }

  const shortEMA = calculateEMA(prices.slice(-shortPeriod - 1), shortPeriod)
  const longEMA = calculateEMA(prices.slice(-longPeriod - 1), longPeriod)
  const macd = shortEMA - longEMA

  const macdValues = prices.slice(-longPeriod - signalPeriod).map((_, i) => {
    const slice = prices.slice(i, i + longPeriod)
    return calculateEMA(slice, shortPeriod) - calculateEMA(slice, longPeriod)
  })
  const signal = calculateEMA(macdValues, signalPeriod)

  return { macd, signal }
}

// Hàm tính ATR
export function calculateATR(candles: CandleChartResult[], period: number): number {
  if (candles.length < period + 1) return NaN
  const trValues = candles
    .slice(-period - 1)
    .map((candle, index) => {
      if (index === 0) return 0
      const high = parseFloat(candle.high)
      const low = parseFloat(candle.low)
      const prevClose = parseFloat(candles[index - 1].close)
      return Math.max(high - low, Math.abs(high - prevClose), Math.abs(low - prevClose))
    })
    .slice(1)
  return trValues.reduce((acc, val) => acc + val, 0) / period
}

// Hàm tạo tín hiệu giao dịch
export async function generateTradingSignal(candles: CandleChartResult[]): Promise<TradingSignal> {
  // Tham số
  const atrPeriod = 14
  const atrMultiplierSL = 1.5 // Giảm để chặt chẽ hơn
  const atrMultiplierTP = 4.5 // Tăng để đạt R:R 1:3

  // Kiểm tra dữ liệu đầu vào
  if (!candles || candles.length < 26 + 9) {
    // Đủ cho MACD
    console.log('Không đủ dữ liệu nến để tạo tín hiệu giao dịch.')
    return { positionSide: null, entryPrice: null, takeProfitPrice: null, stopLossPrice: null }
  }

  // Lấy giá đóng cửa
  const closePrices = candles.map(candle => parseFloat(candle.close))

  // Tính MACD
  const { macd, signal } = calculateMACD(closePrices)
  const prevMACD = calculateMACD(closePrices.slice(0, -1)).macd
  const prevSignal = calculateMACD(closePrices.slice(0, -1)).signal

  // Tính ATR
  const atr = calculateATR(candles, atrPeriod)

  // Lấy thông tin nến cuối
  const lastCandle = candles[candles.length - 1]
  const entryPrice = parseFloat(lastCandle.close)

  // Kiểm tra tín hiệu Long
  if (prevMACD < prevSignal && macd > signal) {
    const stopLossPrice = entryPrice - atr * atrMultiplierSL
    const takeProfitPrice = entryPrice + atr * atrMultiplierTP

    console.log(`🍖 Trading signal: `, {
      positionSide: 'LONG',
      entryPrice,
      takeProfitPrice,
      stopLossPrice,
    })

    return {
      positionSide: 'LONG',
      entryPrice,
      takeProfitPrice,
      stopLossPrice,
    }
  }
  // Kiểm tra tín hiệu Short
  else if (prevMACD > prevSignal && macd < signal) {
    const stopLossPrice = entryPrice + atr * atrMultiplierSL
    const takeProfitPrice = entryPrice - atr * atrMultiplierTP

    console.log(`🍖 Trading signal: `, {
      positionSide: 'SHORT',
      entryPrice,
      takeProfitPrice,
      stopLossPrice,
    })

    return {
      positionSide: 'SHORT',
      entryPrice,
      takeProfitPrice,
      stopLossPrice,
    }
  }

  // Không có tín hiệu
  return { positionSide: null, entryPrice: null, takeProfitPrice: null, stopLossPrice: null }
}
