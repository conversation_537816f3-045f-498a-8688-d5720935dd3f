import { CandleChartResult } from 'binance-api-node'
import { TradingSignal } from './main/utils/trade_model'

function calculateEMA(prices: number[], period: number): number {
  if (prices.length < period) return NaN
  const k = 2 / (period + 1)
  let ema = prices[0]
  for (let i = 1; i < prices.length; i++) {
    ema = prices[i] * k + ema * (1 - k)
  }
  return ema
}

// Calculate MACD
function calculateMACD(
  prices: number[],
  fastPeriod: number,
  slowPeriod: number,
  signalPeriod: number,
): { macd: number; signal: number; histogram: number } {
  const fastEMA = calculateEMA(prices, fastPeriod)
  const slowEMA = calculateEMA(prices, slowPeriod)
  const macd = fastEMA - slowEMA

  // Calculate signal line (EMA of MACD)
  // For simplicity, we'll use a simple approximation
  const signal = macd * 0.8 // Approximation of signal line
  const histogram = macd - signal

  return { macd, signal, histogram }
}

// Tính RSI trên window giá gần nhất (sử dụng đúng rsiPeriod giá cuối)
function calculateRSI(prices: number[], period: number): number {
  if (prices.length < period) return NaN
  const windowPrices = prices.slice(-period)
  let gains = 0
  let losses = 0
  for (let i = 1; i < windowPrices.length; i++) {
    const change = windowPrices[i] - windowPrices[i - 1]
    if (change > 0) gains += change
    else losses -= change
  }
  const avgGain = gains / (period - 1)
  const avgLoss = losses / (period - 1)
  if (avgLoss === 0) return 100
  const rs = avgGain / avgLoss
  return 100 - 100 / (1 + rs)
}

// Tính ATR từ mảng nến cho số chu kỳ nhất định
function calculateATR(candles: CandleChartResult[], period: number): number {
  if (candles.length < period) return NaN
  let trSum = 0
  for (let i = 1; i < period; i++) {
    const high = parseFloat(candles[i].high)
    const low = parseFloat(candles[i].low)
    const prevClose = parseFloat(candles[i - 1].close)
    const tr = Math.max(high - low, Math.abs(high - prevClose), Math.abs(low - prevClose))
    trSum += tr
  }
  return trSum / (period - 1)
}

// Calculate Bollinger Bands
function calculateBollingerBands(
  prices: number[],
  period: number,
  multiplier: number,
): { upper: number; middle: number; lower: number } {
  // Calculate SMA
  const sma = prices.slice(-period).reduce((sum, price) => sum + price, 0) / period

  // Calculate standard deviation
  const squaredDifferences = prices.slice(-period).map(price => Math.pow(price - sma, 2))
  const variance = squaredDifferences.reduce((sum, val) => sum + val, 0) / period
  const stdDev = Math.sqrt(variance)

  // Calculate bands
  const upper = sma + multiplier * stdDev
  const lower = sma - multiplier * stdDev

  return { upper, middle: sma, lower }
}

// Check for volume spike
function isVolumeSpikeUp(
  candles: CandleChartResult[],
  period: number,
  multiplier: number,
): boolean {
  if (candles.length < period + 1) return false

  const volumes = candles.slice(-period - 1, -1).map(c => parseFloat(c.volume))
  const avgVolume = volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length
  const currentVolume = parseFloat(candles[candles.length - 1].volume)

  return currentVolume > avgVolume * multiplier
}

// Detect price momentum
function detectMomentum(prices: number[], period: number): number {
  if (prices.length < period) return 0

  const recentPrices = prices.slice(-period)
  const firstPrice = recentPrices[0]
  const lastPrice = recentPrices[recentPrices.length - 1]

  return ((lastPrice - firstPrice) / firstPrice) * 100
}

// --- Hàm tạo tín hiệu giao dịch từ một window nến ---
export function generateTradingSignal(windowCandles: CandleChartResult[]): TradingSignal {
  // Tham số tối ưu hóa
  const emaShortPeriod = 8
  const emaLongPeriod = 21
  const rsiPeriod = 14
  const atrPeriod = 14
  const macdFast = 12
  const macdSlow = 26
  const macdSignal = 9
  const bbPeriod = 20
  const bbMultiplier = 2.5
  const volumePeriod = 10
  const volumeMultiplier = 1.5
  const momentumPeriod = 10

  // Kiểm tra đủ dữ liệu cho window
  const requiredPeriod = Math.max(emaLongPeriod + 1, rsiPeriod, atrPeriod, bbPeriod, macdSlow)
  if (windowCandles.length < requiredPeriod) {
    return { positionSide: null, entryPrice: null, stopLossPrice: null, takeProfitPrice: null }
  }

  // Lấy giá đóng cửa của window
  const closePrices = windowCandles.map(candle => parseFloat(candle.close))
  // Để so sánh hiện tại và cây nến liền trước, ta lấy window gồm (emaLongPeriod + 1) cây nến cuối
  const recentCandles = windowCandles.slice(-(emaLongPeriod + 1))
  const recentClose = recentCandles.map(c => parseFloat(c.close))

  // Tính EMA hiện tại và trước đó
  const emaShort = calculateEMA(recentClose, emaShortPeriod)
  const emaLong = calculateEMA(recentClose, emaLongPeriod)
  const prevClose = recentClose.slice(0, -1)
  const prevEmaShort = calculateEMA(prevClose, emaShortPeriod)
  const prevEmaLong = calculateEMA(prevClose, emaLongPeriod)

  // Tính RSI
  const rsi = calculateRSI(closePrices, rsiPeriod)

  // Tính MACD
  const macd = calculateMACD(closePrices, macdFast, macdSlow, macdSignal)

  // Tính Bollinger Bands
  const bb = calculateBollingerBands(closePrices, bbPeriod, bbMultiplier)

  // Kiểm tra volume spike
  const hasVolumeSpikeUp = isVolumeSpikeUp(windowCandles, volumePeriod, volumeMultiplier)

  // Tính momentum
  const momentum = detectMomentum(closePrices, momentumPeriod)

  // Tính ATR dựa trên toàn bộ window
  const atr = calculateATR(windowCandles, atrPeriod)

  // Giá vào lệnh (sử dụng giá đóng cửa của cây nến cuối trong window)
  const entryPrice = parseFloat(windowCandles[windowCandles.length - 1].close)
  const currentPrice = entryPrice

  // --- Điều kiện tạo tín hiệu LONG ---
  // 1. EMA crossover (short crosses above long)
  // 2. RSI < 30 (oversold)
  // 3. Price near lower Bollinger Band
  // 4. MACD histogram turning positive
  // 5. Volume spike up (optional)
  // 6. Positive momentum
  if (
    prevEmaShort < prevEmaLong &&
    emaShort > emaLong &&
    rsi < 35 &&
    currentPrice < bb.lower * 1.03 && // Price near or below lower BB
    macd.histogram > 0 &&
    momentum > 0
    // Volume spike is optional for LONG
  ) {
    // Tính stop loss và take profit
    const stopLossPrice = entryPrice - 0.6 * atr
    const takeProfitPrice = entryPrice + 5.0 * atr // Risk:Reward = 1:8.3

    console.log(
      `🧪 Trading signal LONG at price ${entryPrice.toFixed(
        4,
      )} with stop loss ${stopLossPrice.toFixed(4)} and take profit ${takeProfitPrice.toFixed(
        4,
      )} (RSI: ${rsi.toFixed(2)})`,
    )

    return {
      positionSide: 'LONG',
      entryPrice,
      stopLossPrice,
      takeProfitPrice,
    }
  }
  // --- Điều kiện tạo tín hiệu SHORT ---
  // 1. EMA crossover (short crosses below long)
  // 2. RSI > 70 (overbought)
  // 3. Price near upper Bollinger Band
  // 4. MACD histogram turning negative
  // 5. Volume spike up (required)
  // 6. Negative momentum
  else if (
    prevEmaShort > prevEmaLong &&
    emaShort < emaLong &&
    rsi > 65 &&
    currentPrice > bb.upper * 0.97 && // Price near or above upper BB
    macd.histogram < 0 &&
    momentum < 0 &&
    hasVolumeSpikeUp // Volume spike required for SHORT
  ) {
    // Tính stop loss và take profit
    const stopLossPrice = entryPrice + 0.6 * atr
    const takeProfitPrice = entryPrice - 5.0 * atr // Risk:Reward = 1:8.3

    console.log(
      `🌡️ Trading signal SHORT at price ${entryPrice.toFixed(
        4,
      )} with stop loss ${stopLossPrice.toFixed(4)} and take profit ${takeProfitPrice.toFixed(
        4,
      )} (RSI: ${rsi.toFixed(2)})`,
    )

    return {
      positionSide: 'SHORT',
      entryPrice,
      stopLossPrice,
      takeProfitPrice,
    }
  }

  // Không có tín hiệu
  return { positionSide: null, entryPrice: null, stopLossPrice: null, takeProfitPrice: null }
}
