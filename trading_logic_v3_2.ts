import { CandleChartResult } from 'binance-api-node'
import { ATR, EMA, RSI } from 'technicalindicators'

// Định nghĩa interface cho tín hiệu giao dịch
export interface TradingSignal {
  positionSide: 'LONG' | 'SHORT' | null
  entryPrice: number | null
  takeProfitPrice: number | null
  stopLossPrice: number | null
}

// Hàm tính EMA
function calculateEMA(prices: number[], period: number): number {
  const emaValues = EMA.calculate({ values: prices, period })
  return emaValues[emaValues.length - 1]
}

// Hàm tính RSI
function calculateRSI(prices: number[], period: number): number {
  const rsiValues = RSI.calculate({ values: prices, period })
  return rsiValues[rsiValues.length - 1]
}

// Hàm tính ATR
function calculateATR(candles: CandleChartResult[], period: number): number {
  const atrValues = ATR.calculate({
    high: candles.map(c => parseFloat(c.high)),
    low: candles.map(c => parseFloat(c.low)),
    close: candles.map(c => parseFloat(c.close)),
    period,
  })
  return atrValues[atrValues.length - 1]
}

// Hàm tạo tín hiệu giao dịch
export async function generateTradingSignal(candles: CandleChartResult[]): Promise<TradingSignal> {
  // Tham số
  const shortEMAPeriod = 10 // EMA ngắn hạn: 10 phút
  const longEMAPeriod = 20 // EMA dài hạn: 20 phút
  const rsiPeriod = 14
  const atrPeriod = 14

  // Kiểm tra dữ liệu đầu vào
  if (!candles || candles.length < longEMAPeriod) {
    console.log('Không đủ dữ liệu nến để tạo tín hiệu giao dịch.')
    return { positionSide: null, entryPrice: null, takeProfitPrice: null, stopLossPrice: null }
  }

  // Lấy giá đóng cửa
  const closePrices = candles.map(candle => parseFloat(candle.close))

  // Tính EMA hiện tại và EMA trước đó
  const shortEMA = calculateEMA(closePrices, shortEMAPeriod)
  const longEMA = calculateEMA(closePrices, longEMAPeriod)
  const prevShortEMA = calculateEMA(closePrices.slice(0, -1), shortEMAPeriod)
  const prevLongEMA = calculateEMA(closePrices.slice(0, -1), longEMAPeriod)

  // Tính RSI và ATR
  const rsi = calculateRSI(closePrices, rsiPeriod)
  const atr = calculateATR(candles, atrPeriod)

  // Lấy thông tin nến cuối
  const lastCandle = candles[candles.length - 1]
  const entryPrice = parseFloat(lastCandle.close)

  // Kiểm tra tín hiệu LONG
  if (prevShortEMA <= prevLongEMA && shortEMA > longEMA && rsi < 25) {
    const stopLossPrice = entryPrice - 1.5 * atr
    const takeProfitPrice = entryPrice + 2 * atr

    console.log(
      `🍖 Tín hiệu LONG: Giá vào lệnh ${entryPrice}, Take Profit ${takeProfitPrice}, Stop Loss ${stopLossPrice}`,
    )
    return { positionSide: 'LONG', entryPrice, takeProfitPrice, stopLossPrice }
  }
  // Kiểm tra tín hiệu SHORT
  else if (prevShortEMA >= prevLongEMA && shortEMA < longEMA && rsi > 75) {
    const stopLossPrice = entryPrice + 1.5 * atr
    const takeProfitPrice = entryPrice - 2 * atr

    console.log(
      `🍖 Tín hiệu SHORT: Giá vào lệnh ${entryPrice}, Take Profit ${takeProfitPrice}, Stop Loss ${stopLossPrice}`,
    )
    return { positionSide: 'SHORT', entryPrice, takeProfitPrice, stopLossPrice }
  }

  // Không có tín hiệu
  return { positionSide: null, entryPrice: null, takeProfitPrice: null, stopLossPrice: null }
}
