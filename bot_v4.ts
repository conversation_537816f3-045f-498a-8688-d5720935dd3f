import csv from 'csv-parser'
import * as fs from 'fs'
import client from './main/utils/client.ts'

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho mỗi cây nến
interface Candle {
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume: number
}

// Hàm đọc dữ liệu lịch sử từ file CSV
function loadHistoricalData(filePath: string): Promise<Candle[]> {
  return new Promise((resolve, reject) => {
    const results: Candle[] = []
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', data => {
        results.push({
          timestamp: Number(data.timestamp),
          open: Number(data.open),
          high: Number(data.high),
          low: Number(data.low),
          close: Number(data.close),
          volume: Number(data.volume),
        })
      })
      .on('end', () => {
        resolve(results)
      })
      .on('error', reject)
  })
}

// Hàm tính trung bình động (MA) – ví dụ sử dụng cho tín hiệu
function movingAverage(data: Candle[], period: number, index: number): number | null {
  if (index < period - 1) return null
  const subset = data.slice(index - period + 1, index + 1)
  const sum = subset.reduce((acc, candle) => acc + candle.close, 0)
  return sum / period
}

// Mô phỏng chiến lược với tham số takeProfitRatio và stopLossRatio
interface BacktestResult {
  profitPercent: number
  trades: number
}

function backtestStrategy(
  data: Candle[],
  takeProfitRatio: number,
  stopLossRatio: number,
): BacktestResult {
  let capital = 1 // giả sử vốn ban đầu là 1 (đơn vị)
  const leverage = 20
  let inPosition = false
  let entryPrice = 0
  let trades = 0

  // Ví dụ sử dụng tín hiệu đơn giản dựa trên MA
  const maPeriod = 20 // ví dụ 20 phút
  for (let i = maPeriod; i < data.length; i++) {
    // signal: nếu giá đóng cửa vượt trên MA, mở vị thế long (và ngược lại với short nếu cần)
    const ma = movingAverage(data, maPeriod, i)
    if (ma === null) continue
    const currentPrice = data[i].close

    // Nếu chưa có vị thế, vào lệnh khi có tín hiệu
    if (!inPosition && currentPrice > ma) {
      inPosition = true
      entryPrice = currentPrice
      trades++
      // Giả sử đặt lệnh stop loss và take profit ngay sau khi vào lệnh
    }

    if (inPosition) {
      // Tính mục tiêu chốt lời và cắt lỗ dựa trên giá vào lệnh
      const tpPrice = entryPrice * (1 + takeProfitRatio)
      const slPrice = entryPrice * (1 - stopLossRatio)

      // Kiểm tra điều kiện chốt lời hoặc cắt lỗ
      if (data[i].high >= tpPrice) {
        // Lợi nhuận theo đòn bẩy
        const profit = ((tpPrice - entryPrice) / entryPrice) * leverage
        capital *= 1 + profit
        inPosition = false
        continue
      }
      if (data[i].low <= slPrice) {
        // Lỗ theo đòn bẩy
        const loss = ((entryPrice - slPrice) / entryPrice) * leverage
        capital *= 1 - loss
        inPosition = false
        continue
      }
    }
  }

  return {
    profitPercent: (capital - 1) * 100,
    trades,
  }
}

// Hàm tối ưu tham số dựa trên dữ liệu lịch sử
async function optimizeParameters(
  data: Candle[],
): Promise<{ bestTP: number; bestSL: number; result: BacktestResult }> {
  let bestResult: BacktestResult = { profitPercent: -Infinity, trades: 0 }
  let bestTP = 0
  let bestSL = 0

  // Ví dụ quét tham số từ 0.1% đến 5% (0.001 đến 0.05) với bước 0.001
  for (let tp = 0.001; tp <= 0.05; tp += 0.001) {
    for (let sl = 0.001; sl <= 0.05; sl += 0.001) {
      const result = backtestStrategy(data, tp, sl)
      // Nếu đạt mục tiêu lợi nhuận 500% và tốt hơn so với kết quả trước, cập nhật
      if (result.profitPercent >= 500 && result.profitPercent > bestResult.profitPercent) {
        bestResult = result
        bestTP = tp
        bestSL = sl
      }
    }
  }

  return { bestTP, bestSL, result: bestResult }
}

// Phần khởi tạo và chạy bot giao dịch với Binance Futures
async function runTradingBot() {
  // Nạp dữ liệu lịch sử từ file CSV
  const historicalData = await loadHistoricalData('./historical_data.csv')
  console.log(`Loaded ${historicalData.length} candles`)

  // Tối ưu tham số dựa trên dữ liệu lịch sử
  const { bestTP, bestSL, result } = await optimizeParameters(historicalData)
  console.log(
    `Tối ưu: Take Profit Ratio = ${bestTP}, Stop Loss Ratio = ${bestSL}, Profit = ${result.profitPercent.toFixed(
      2,
    )}%, Trades: ${result.trades}`,
  )

  // Ở đây, bạn có thể chuyển sang chế độ giao dịch thực (live trading)
  // Ví dụ: đặt lệnh mở vị thế dựa trên tín hiệu (dùng logic tương tự trong backtest)
  // Chỉ mở 1 vị thế tại 1 thời điểm
  let inPosition = false

  // Giả sử bạn sử dụng websocket hoặc polling để nhận dữ liệu thị trường realtime
  client.ws.candles('BTCUSDT', '1m', candle => {
    // Mỗi cây nến nhận được
    const currentCandle = {
      timestamp: candle.startTime,
      open: parseFloat(candle.open),
      high: parseFloat(candle.high),
      low: parseFloat(candle.low),
      close: parseFloat(candle.close),
      volume: parseFloat(candle.volume),
    }

    // Logic tín hiệu đơn giản: nếu giá đóng cửa vượt trên MA (tính theo dữ liệu realtime)
    // Ở đây bạn cần lưu trữ lịch sử cây nến realtime để tính MA (giả sử đã có mảng candlesRealtime)
    // Để đơn giản, ta chỉ minh họa phần đặt lệnh khi có tín hiệu
    if (!inPosition && someTradingSignal(currentCandle)) {
      // Giả sử mở lệnh long
      inPosition = true
      const entryPrice = currentCandle.close

      // Tính giá chốt lời và cắt lỗ
      const tpPrice = entryPrice * (1 + bestTP)
      const slPrice = entryPrice * (1 - bestSL)

      // Ví dụ đặt lệnh với đòn bẩy tối đa 20x (lệnh Futures)
      // Đặt lệnh market để vào vị thế
      client
        .futuresOrder({
          symbol: 'BTCUSDT',
          side: 'BUY',
          type: 'MARKET',
          quantity: calculateOrderQuantity(entryPrice), // Hàm tự định nghĩa để tính số lượng theo vốn hiện có
          leverage: 20,
        })
        .then(order => {
          console.log('Lệnh mở vị thế thành công:', order)

          // Sau khi vào lệnh, đặt lệnh stop loss và take profit
          // Đây là ví dụ đặt lệnh OCO (One Cancels the Other) nếu API hỗ trợ, hoặc đặt riêng 2 lệnh
          client.futuresOrder({
            symbol: 'BTCUSDT',
            side: 'SELL',
            type: 'STOP_MARKET',
            stopPrice: slPrice.toFixed(2),
            quantity: calculateOrderQuantity(entryPrice),
          })
          client.futuresOrder({
            symbol: 'BTCUSDT',
            side: 'SELL',
            type: 'TAKE_PROFIT_MARKET',
            stopPrice: tpPrice.toFixed(2),
            quantity: calculateOrderQuantity(entryPrice),
          })
        })
        .catch(err => {
          console.error('Lỗi khi đặt lệnh:', err)
        })
    }
  })
}

// Hàm giả lập cho tín hiệu giao dịch – cần phát triển logic thực tế
function someTradingSignal(candle: Candle): boolean {
  // Ví dụ: nếu giá đóng cửa vượt trên một ngưỡng nhất định (có thể kết hợp với MA,...)
  // Đây chỉ là ví dụ đơn giản
  return candle.close > candle.open
}

// Hàm tính số lượng lệnh dựa vào vốn và giá hiện tại
function calculateOrderQuantity(price: number): string {
  // Ví dụ đơn giản: sử dụng 1% vốn cho mỗi giao dịch
  // Bạn cần lấy số dư tài khoản thực tế và tính toán số lượng dựa trên đòn bẩy
  const positionValue = 0.01 // 1% của vốn
  return (positionValue / price).toFixed(3)
}

// Chạy bot (có thể bổ sung thêm logic dừng, xử lý lỗi,...)
runTradingBot().catch(err => console.error(err))
