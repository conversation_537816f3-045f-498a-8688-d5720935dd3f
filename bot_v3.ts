// @ts-check
import { CandleChartResult, FuturesBalanceResult, PositionRiskResult } from 'binance-api-node'

import { setIntervalAsync } from 'set-interval-async'
import { BalanceLogger } from './main/log/balance_logger.ts'
import client from './main/utils/client.ts'
import { TradingSignal } from './main/utils/trade_model.ts'
import {
  cancelAllOrders,
  getCurrentPosition,
  placeLimitOrder,
  startTrailingStop,
} from './main/utils/trade_utils.ts'
import { generateTradingSignal } from './trading_logic_v3.ts'

const logger = new BalanceLogger('./main/log/balance_v3_log.csv')

const SYMBOL = 'BTCUSDT'
const LEVERAGE = 10
const ACCOUNT_PERCENTAGE = 1
const STOP_LOSS_PERCENT = 0.04 // 2%
const TAKE_PROFIT_PERCENT = 0.1 // 5%
const RISK_REWARD_RATIO = 1.5 // R:R = 1:1.5
const INTERVAL = '1m'

async function checkBalance() {
  const balance = await getTotalBalance()
  console.log(`💰 Current Balance: ${balance.toFixed(2)} USDT`)
  logger.logBalance(balance)
}

async function getTotalBalance() {
  const accountInfo: FuturesBalanceResult[] = await client.futuresAccountBalance()
  const usdtBalance = accountInfo.find(item => item.asset === 'USDT')

  return parseFloat(usdtBalance?.balance ?? '0')
}

// Hàm tính toán kích thước lệnh
async function calculateOrderSize() {
  const usdtBalance = await getTotalBalance()
  const orderValue = usdtBalance * ACCOUNT_PERCENTAGE
  console.log('Order Value: ', orderValue)
  return orderValue
}

async function openPosition(signal: TradingSignal) {
  if (signal.positionSide == null) {
    console.log('No position side')
    return
  }

  if (signal.entryPrice == null) {
    console.log('Entry price is null')
    return
  }

  await cancelAllOrders(SYMBOL)

  const orderSize = await calculateOrderSize()
  const quantity = ((orderSize * LEVERAGE) / signal.entryPrice).toFixed(3)

  console.log('====================================')
  console.log('Position side: ', signal.positionSide)
  console.log('Quantity: ', quantity)
  console.log('Entry price: ', signal.entryPrice)
  console.log('Take Profit price: ', signal.takeProfitPrice)
  console.log('Stop Loss price: ', signal.stopLossPrice)
  console.log('')

  if (signal.positionSide === 'LONG') {
    await placeLimitOrder(signal, 'BUY', quantity, SYMBOL)
  } else if (signal.positionSide === 'SHORT') {
    await placeLimitOrder(signal, 'SELL', quantity, SYMBOL)
  }

  // placeSLOrder(signal, quantity, SYMBOL)
  // placeTPOrder(signal, quantity, SYMBOL)

  startTrailingStop(signal.positionSide, signal.entryPrice, quantity, SYMBOL)

  console.log('====================================')
}

async function trade() {
  const candles: CandleChartResult[] = await client.futuresCandles({
    symbol: SYMBOL,
    interval: INTERVAL,
    limit: 100,
  })
  const signal = await generateTradingSignal(candles)
  const position: PositionRiskResult | null = await getCurrentPosition(SYMBOL)

  if (position != null) return

  checkBalance()

  openPosition(signal)
}

;(async function() {
  console.log('=== Bot is running ===')
  console.log('test connect binance', await client.ping())
  await client.futuresLeverage({ symbol: SYMBOL, leverage: LEVERAGE })

  trade()
  setIntervalAsync(async () => {
    trade()
  }, 15000)
})()
