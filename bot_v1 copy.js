
const Binance = require('binance-api-node').default;
const { setIntervalAsync } = require('set-interval-async/dynamic');
const { loadApiKeys } = require('./config');
const { OrderSide, OrderType } = require('binance-api-node');
const BalanceLogger = require('./main/log/balance_logger');

const logger = new BalanceLogger('balance_log.csv');
const { apiKey, apiSecret, httpFutures, wsFutures } = loadApiKeys();

const client = Binance({
  apiKey,
  apiSecret,
  httpFutures,
  wsFutures,
});


const SYMBOL = 'BTCUSDT';
const LEVERAGE = 20;
const ACCOUNT_PERCENTAGE = 0.2;
const STOP_LOSS_PERCENT = 0.04; // 4%
const TAKE_PROFIT_PERCENT = 0.1; // 10%
const INTERVAL = '1m';
let positionOpen = false;
let currentPosition = null;
let currentQuantity = 0;

function getUSDTBalance(balances) {
  const usdtItem = balances.find(item => item.asset === 'USDT');
  if (usdtItem) {
    return parseFloat(usdtItem.balance);
  } else {
    return NaN;
  }
}


async function checkBalance() {
  const balance = await getTotalBalance();
  console.log(`Current Balance: ${balance.toFixed(2)} USDT`);
  logger.logBalance(balance);
}

async function getTotalBalance() {
  const accountInfo = await client.futuresAccountBalance();
  const usdtBalance = parseFloat(getUSDTBalance(accountInfo));

  return usdtBalance;
}

async function calculateOrderSize() {
  const usdtBalance = await getTotalBalance();
  const orderValue = usdtBalance * ACCOUNT_PERCENTAGE;
  console.log("Order Value: ", orderValue);
  return orderValue;
}

/**
 * Calculate RSI
 * @param {Array<number>} prices - Array of closing prices
 * @param {number} period - The period for RSI calculation (e.g., 14)
 * @returns {number} - The calculated RSI value
 */
function calculateRSI(prices, period = 14) {
  if (prices.length < period + 1) {
    throw new Error('Not enough data to calculate RSI');
  }

  // Step 1: Calculate initial gains and losses
  let gains = 0;
  let losses = 0;

  for (let i = 1; i <= period; i++) {
    const change = prices[i] - prices[i - 1];
    if (change > 0) {
      gains += change;
    } else {
      losses -= change; // Loss is negative change
    }
  }

  // Step 2: Calculate initial average gains and losses
  let avgGain = gains / period;
  let avgLoss = losses / period;

  // Step 3: Calculate RSI for the rest of the prices
  for (let i = period + 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    const gain = change > 0 ? change : 0;
    const loss = change < 0 ? -change : 0;

    // Smooth average gains and losses
    avgGain = (avgGain * (period - 1) + gain) / period;
    avgLoss = (avgLoss * (period - 1) + loss) / period;
  }

  // Step 4: Calculate RS and RSI
  const rs = avgGain / avgLoss || 0; // Prevent division by zero
  const rsi = 100 - 100 / (1 + rs);

  return rsi;
}

// Hàm tính toán Bollinger Bands
function calculateBollingerBands(data, period, stdDev) {
  const sma = data.slice(-period).reduce((sum, val) => sum + val, 0) / period;
  const variance = data.slice(-period).reduce((sum, val) => sum + Math.pow(val - sma, 2), 0) / period;
  const std = Math.sqrt(variance);
  return {
    middle: sma,
    upper: sma + stdDev * std,
    lower: sma - stdDev * std,
  };
}


async function cancelAllOrders() {
  const openOrders = await client.futuresOpenOrders({ symbol: SYMBOL });
  for (const order of openOrders) {
    await client.futuresCancelOrder({ symbol: SYMBOL, orderId: order.orderId });
  }
  console.log('Đã hủy tất cả các lệnh đang mở.');
}


async function openPosition(signal) {
  if (signal == 'NONE') return;

  await cancelAllOrders();

  const orderSize = await calculateOrderSize();
  const price = parseFloat((await client.futuresPrices())[SYMBOL]);
  const quantity = (orderSize * LEVERAGE / price).toFixed(3);
  currentQuantity = quantity;

  console.log("quantity: ", quantity);
  console.log("price: ", price);
  console.log("signal: ", signal);

  if (signal === 'LONG') {
    console.log("** open long position");
    await client.futuresOrder({
      symbol: SYMBOL,
      side: 'BUY',
      type: 'MARKET',
      quantity,
    });
  } else {
    console.log("** open short position");
    await client.futuresOrder({
      symbol: SYMBOL,
      side: 'SELL',
      type: 'MARKET',
      quantity,
    });
  }


  const stopLossAmount = orderSize * STOP_LOSS_PERCENT;
  const takeProfitAmount = orderSize * TAKE_PROFIT_PERCENT;


  let stopLossPrice = signal === 'LONG'
    ? price - (stopLossAmount / quantity)
    : price + (stopLossAmount / quantity)
  stopLossPrice = stopLossPrice.toFixed(0);

  let takeProfitPrice = signal === 'LONG'
    ? price + (takeProfitAmount / quantity)
    : price - (takeProfitAmount / quantity)
  takeProfitPrice = takeProfitPrice.toFixed(0);

  console.log("** stopLossPrice: ", stopLossPrice);
  await client.futuresOrder({
    symbol: SYMBOL,
    side: signal === 'LONG' ? 'SELL' : 'BUY',
    type: 'STOP',
    price: stopLossPrice,
    stopPrice: stopLossPrice,
    quantity,
  });
  console.log("** Done stopLossPrice: ", stopLossPrice);

  console.log("** takeProfitPrice: ", takeProfitPrice);
  await client.futuresOrder({
    symbol: SYMBOL,
    side: signal === 'LONG' ? 'SELL' : 'BUY',
    type: 'TAKE_PROFIT',
    price: takeProfitPrice,
    stopPrice: takeProfitPrice,
    quantity,
  });
  console.log("** Done takeProfitPrice: ", takeProfitPrice);


  positionOpen = true;
  currentPosition = signal;
}


async function getCurrentPosition() {
  const positions = await client.futuresPositionRisk();
  const position = positions.find(pos => pos.symbol === SYMBOL);
  console.log("position", position)
  if (position && parseFloat(position.positionAmt) != 0) {
    const size = parseFloat(position.positionAmt);
    const entryPrice = parseFloat(position.entryPrice);
    const unrealizedProfit = parseFloat(position.unRealizedProfit);

    console.log(`Current Position: ${size}, Entry Price: ${entryPrice}, Unrealized Profit: ${unrealizedProfit}`);

    currentPosition = position;
    positionOpen = true;
  } else {
    console.log('No Position Open');
    currentPosition = null;
    positionOpen = false;
    return null;
  }
}

async function calculateIndicators(data) {
  const prices = data.map(candle => parseFloat(candle.close));
  const rsi = calculateRSI(prices, 14); // Calculate RSI
  const bb = calculateBollingerBands(prices, 20, 2); // Calculate Bollinger Bands

  return { rsi, bb };
}

// Main logic
async function tradeLogic() {
  const candles = await client.futuresCandles({ symbol: SYMBOL, interval: INTERVAL, limit: 100 });
  const indicators = await calculateIndicators(candles);

  const latestCandle = candles[candles.length - 1];
  const currentPrice = parseFloat(latestCandle.close);

  // Long condition
  if (indicators.rsi < 40 && currentPrice < indicators.bb.lower * 1.1) {
    console.log('Signal: Buy (Long)');

    return {
      signal: 'LONG',
      price: currentPrice,
    }
  }

  // Short condition
  if (indicators.rsi > 60 && currentPrice > indicators.bb.upper * 0.95) {
    console.log('Signal: Sell (Short)');
    return {
      signal: 'SHORT',
      price: currentPrice,
    }
  }

  return { signal: 'NONE' };
}

async function runBot() {
  console.log("=====================================");
  await checkBalance();
  await getCurrentPosition();
  if (!positionOpen) {
    const { signal } = await tradeLogic();
    await openPosition(signal);
  } else {
    // await closePositionCheck();
  }
  console.log("=====================================");
}

(async function () {
  console.log("=== Bot is running ===");
  console.log("test connect binance", await client.ping())
  await client.futuresLeverage({ symbol: SYMBOL, leverage: LEVERAGE });
  runBot();
  setIntervalAsync(async () => {
    runBot();
  }, 30000);
})();
