import { CandleChartInterval, CandleChartResult } from 'binance-api-node'
import fs from 'fs'
import client from '../utils/client.ts'

// Configuration
const SYMBOL: string = 'BTCUSDT'
const INTERVAL: string = '1M' // 1-minute candles
const OUTPUT_FILE: string = 'jun_1-19_15m.csv'
const API_LIMIT: number = 1000 // Maximum candles per API request

/**
 * Convert interval to milliseconds.
 * @param {string} interval
 * @returns {number}
 */
function intervalToMilliseconds(interval: string): number {
  const msMap: { [key: string]: number } = {
    '1m': 60000,
    '3m': 180000,
    '5m': 300000,
    '15m': 900000,
    '30m': 1800000,
    '1h': 3600000,
    '2h': 7200000,
    '4h': 14400000,
    '6h': 21600000,
    '8h': 28800000,
    '12h': 43200000,
    '1d': 86400000,
    '3d': 259200000,
    '1w': 604800000,
    '1M': 2592000000,
  }
  return msMap[interval]
}

/**
 * Fetch historical data from Binance.
 * @param {string} symbol
 * @param {string} interval
 * @param {number} startTime
 * @param {number} endTime
 * @returns {Promise<Candle[]>}
 */
async function fetchHistoricalData(
  symbol: string,
  interval: string,
  startTime: number,
  endTime: number,
): Promise<CandleChartResult[]> {
  const data: CandleChartResult[] = []
  let fetchStartTime: number = startTime

  while (fetchStartTime < endTime) {
    const fetchEndTime: number = Math.min(
      fetchStartTime + API_LIMIT * intervalToMilliseconds(interval),
      endTime,
    )

    console.log(
      `Fetching data from ${new Date(fetchStartTime).toISOString()} to ${new Date(
        fetchEndTime,
      ).toISOString()}`,
    )

    const candles: CandleChartResult[] = await client.candles({
      symbol,
      interval:
        interval == '15m'
          ? CandleChartInterval.FIFTEEN_MINUTES
          : interval == '1w'
          ? CandleChartInterval.ONE_WEEK
          : interval == '1M'
          ? CandleChartInterval.ONE_MONTH
          : interval == '1d'
          ? CandleChartInterval.ONE_DAY
          : interval == '4h'
          ? CandleChartInterval.FOUR_HOURS
          : interval == '1h'
          ? CandleChartInterval.ONE_HOUR
          : interval == '30m'
          ? CandleChartInterval.THIRTY_MINUTES
          : interval == '5m'
          ? CandleChartInterval.FIVE_MINUTES
          : CandleChartInterval.ONE_MONTH,
      startTime: fetchStartTime,
      endTime: fetchEndTime,
      limit: API_LIMIT,
    })

    if (candles.length === 0) {
      break
    }

    data.push(...candles)
    fetchStartTime = candles[candles.length - 1].closeTime + 1 // Continue from the next candle
  }

  return data
}

/**
 * Save historical data to a CSV file.
 * @param {Candle[]} data
 * @param {string} filePath
 */
function saveToCSV(data: CandleChartResult[], filePath: string): void {
  const headers: string[] = ['time', 'open', 'high', 'low', 'close', 'volume']
  const rows: string[][] = data.map(candle => [
    new Date(candle.openTime).toISOString(),
    candle.open,
    candle.high,
    candle.low,
    candle.close,
    candle.volume,
  ])

  const csvContent: string = [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
  fs.writeFileSync(filePath, csvContent, 'utf8')

  console.log(`Saved data to ${filePath}`)
}

/**
 * Main function to fetch and save historical data.
 */
async function main(): Promise<void> {
  try {
    // const now = new Date(2024, 9, 1).getTime()

    const past = new Date(2020, 1, 1)
    const now = new Date(2025, 6, 1)
    const duration: number = now.getTime() - past.getTime()

    console.log('Fetching historical data...')
    const historicalData: CandleChartResult[] = await fetchHistoricalData(
      SYMBOL,
      INTERVAL,
      past.getTime(),
      now.getTime(),
    )

    let name =
      INTERVAL +
      '_' +
      past.getUTCDate() +
      past.getMonth() +
      past.getFullYear() +
      '_' +
      now.getUTCDate() +
      now.getMonth() +
      now.getFullYear() +
      '.csv'

    console.log(`Fetched ${historicalData.length} candles.`)
    saveToCSV(historicalData, name)

    console.log('Done!')
  } catch (err) {
    // Use 'any' or specify the error type if known
    console.error(`Error: ${err}`)
  }
}

main()
