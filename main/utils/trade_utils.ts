import {
  CandleChartResult,
  LimitNewFuturesOrder,
  PositionRiskResult,
  QueryFuturesOrderResult,
  StopNewFuturesOrder,
  TakeProfitMarketNewFuturesOrder,
  TakeProfitNewFuturesOrder,
  Ticker,
} from 'binance-api-node'
import { readFileSync } from 'fs'
import client from './client.ts'
import { TradingSignal } from './trade_model.ts'

export async function cancelAllOrders(symbol: string) {
  const openOrders: QueryFuturesOrderResult[] = await client.futuresOpenOrders({ symbol: symbol })
  for (const order of openOrders) {
    await client.futuresCancelOrder({ symbol: symbol, orderId: parseInt(order.orderId) })
  }
  console.log('Cancel current order')
}

export async function getCurrentPosition(symbol: string): Promise<PositionRiskResult | null> {
  const positions = await client.futuresPositionRisk()
  const position = positions.find(pos => pos.symbol === symbol)
  if (position && parseFloat(position.positionAmt) != 0) {
    const size = parseFloat(position.positionAmt)
    const entryPrice = parseFloat(position.entryPrice)
    const unrealizedProfit = parseFloat(position.unRealizedProfit)

    console.log(
      `📈 Current Position: ${size}, Entry Price: ${entryPrice}, Unrealized Profit: ${unrealizedProfit}`,
    )
    return position
  } else {
    console.log('No Position Open')
    return null
  }
}

const trailingDistancePercent = 1 // Khoảng cách trailing là 1% từ giá cao nhất/thấp nhất

// Hàm triển khai trailing stop
export function startTrailingStop(
  positionSide: 'LONG' | 'SHORT',
  entryPrice: number,
  quantity: string,
  symbol: string,
) {
  let highestPrice = entryPrice // Giá cao nhất (cho LONG)
  let lowestPrice = entryPrice // Giá thấp nhất (cho SHORT)
  let trailingStopPrice: number

  // Tính trailing stop ban đầu
  if (positionSide === 'LONG') {
    trailingStopPrice = entryPrice - (entryPrice * trailingDistancePercent) / 100
  } else {
    trailingStopPrice = entryPrice + (entryPrice * trailingDistancePercent) / 100
  }

  // Theo dõi giá realtime qua WebSocket
  client.ws.ticker(symbol, (ticker: Ticker) => {
    const currentPrice = ticker.eventTime
    console.log(`Current Price: ${currentPrice}`)

    if (positionSide === 'LONG') {
      // Cập nhật giá cao nhất và trailing stop cho LONG
      if (currentPrice > highestPrice) {
        highestPrice = currentPrice
        trailingStopPrice = highestPrice - (highestPrice * trailingDistancePercent) / 100
        console.log(`New Highest Price: ${highestPrice}, Trailing Stop: ${trailingStopPrice}`)
      }

      // Kiểm tra nếu giá chạm trailing stop
      if (currentPrice <= trailingStopPrice) {
        console.log(`Closing LONG position at ${currentPrice}`)
        // Thêm logic đóng vị thế ở đây (ví dụ: gửi lệnh bán qua API)
        return // Thoát khỏi vòng lặp hoặc đóng WebSocket nếu cần
      }
    } else {
      // Cập nhật giá thấp nhất và trailing stop cho SHORT
      if (currentPrice < lowestPrice) {
        lowestPrice = currentPrice
        trailingStopPrice = lowestPrice + (lowestPrice * trailingDistancePercent) / 100
        console.log(`New Lowest Price: ${lowestPrice}, Trailing Stop: ${trailingStopPrice}`)
      }

      // Kiểm tra nếu giá chạm trailing stop
      if (currentPrice >= trailingStopPrice) {
        console.log(`Closing SHORT position at ${currentPrice}`)
        placeMarketOrder(currentPrice.toString(), positionSide, quantity, symbol)
        return // Thoát khỏi vòng lặp hoặc đóng WebSocket nếu cần
      }
    }
  })
}

export async function placeMarketOrder(
  price: string,
  positionSide: 'LONG' | 'SHORT',
  quantity: string,
  symbol: string,
): Promise<void> {
  if (!price) {
    console.error('💢 Error: price is null, cannot place order.')
    return
  }

  const order: TakeProfitMarketNewFuturesOrder = {
    quantity: quantity,
    closePosition: 'true',
    type: 'TAKE_PROFIT_MARKET',
    symbol: symbol,
    stopPrice: price,
    side: positionSide == 'LONG' ? 'SELL' : 'BUY',
  }

  try {
    const orderResponse = await client.futuresOrder(order)
    console.log(`💡 Order TP Market (${positionSide} at ${price})`)
    // Optionally log more details from orderResponse if needed
  } catch (error) {
    console.error(`💢 Error placing ${positionSide}:`, error)
    // Handle error appropriately, e.g., retry logic, error logging, etc.
  }
}

export async function placeLimitOrder(
  signal: TradingSignal,
  side: 'BUY' | 'SELL',
  quantity: string,
  symbol: string,
): Promise<void> {
  if (!signal.entryPrice) {
    console.error('💢 Error: Entry price is null, cannot place order.')
    return // Exit function if entryPrice is missing
  }

  const limitOrder: LimitNewFuturesOrder = {
    type: 'LIMIT',
    timeInForce: 'GTC',
    quantity: quantity,
    price: signal.entryPrice.toFixed(2), // Ensure entryPrice is not null before toFixed
    symbol: symbol,
    side: side,
  }

  try {
    const orderResponse = await client.futuresOrder(limitOrder)
    console.log(
      `💡 Order placed (${side} ${symbol} at ${signal.entryPrice.toFixed(2)}) - Order ID: ${
        orderResponse.orderId
      }`,
    )
    // Optionally log more details from orderResponse if needed
  } catch (error) {
    console.error(`💢 Error placing ${side} order for ${symbol}:`, error)
    // Handle error appropriately, e.g., retry logic, error logging, etc.
  }
}

export async function placeSLOrder(
  signal: TradingSignal,
  quantity: string,
  symbol: string,
): Promise<void> {
  if (signal.stopLossPrice == null) {
    console.error('💢 Error: SL price is null, cannot place order.')
    return
  }

  const order: StopNewFuturesOrder = {
    type: 'STOP',
    quantity: quantity,
    price: signal.stopLossPrice?.toFixed(2),
    stopPrice: signal.stopLossPrice?.toFixed(2),
    symbol: symbol,
    side: signal.positionSide == 'LONG' ? 'SELL' : 'BUY',
  }

  try {
    const orderResponse = await client.futuresOrder(order)
    console.log(
      `💡 SL Order placed (${signal.stopLossPrice.toFixed(2)}) - Order ID: ${
        orderResponse.orderId
      }`,
    )
  } catch (error) {
    console.error(`💢 Error placing SL order:`, error)
  }
}

export async function placeTPOrder(
  signal: TradingSignal,
  quantity: string,
  symbol: string,
): Promise<void> {
  if (signal.takeProfitPrice == null) {
    console.error('💢 Error: TP price is null, cannot place order.')
    return
  }

  const order: TakeProfitNewFuturesOrder = {
    type: 'TAKE_PROFIT',
    quantity: quantity,
    price: signal.takeProfitPrice?.toFixed(2),
    stopPrice: signal.takeProfitPrice?.toFixed(2),
    symbol: symbol,
    side: signal.positionSide == 'LONG' ? 'SELL' : 'BUY',
  }

  try {
    const orderResponse = await client.futuresOrder(order)
    console.log(
      `💡 TP Order placed (${signal.takeProfitPrice.toFixed(2)}) - Order ID: ${
        orderResponse.orderId
      }`,
    )
  } catch (error) {
    console.error(`💢 Error placing TP order: `, error) // Handle error appropriately, e.g., retry logic, error logging, etc.
  }
}

export function loadHistoricalData(filePath: string): CandleChartResult[] {
  console.log(`Reading file from: ${filePath}`)
  try {
    const csvContent = readFileSync(filePath, 'utf8')
    const lines = csvContent.trim().split('\n')
    const headers = lines[0].split(',')
    console.log(`CSV headers: ${headers.join(', ')}`)
    console.log(`Total lines: ${lines.length}`)

    const result = lines.slice(1).map((line, index) => {
      const [time, open, high, low, close, volume] = line.split(',')
      if (index < 3) {
        console.log(
          `Sample data [${index}]: time=${time}, open=${open}, high=${high}, low=${low}, close=${close}, volume=${volume}`,
        )
      }
      // Create a partial CandleChartResult with the available data
      return ({
        openTime: time,
        open,
        high,
        low,
        close,
        volume,
        // Add required properties with default values
        closeTime: 0,
        quoteVolume: '0',
        trades: 0,
        baseAssetVolume: '0',
        quoteAssetVolume: '0',
      } as unknown) as CandleChartResult
    })
    console.log(`Processed ${result.length} candles`)
    return result
  } catch (error) {
    console.error(`Error loading historical data: ${error}`)
    throw error
  }
}
