import fs from 'fs' // Use import * as fs for CommonJS modules in TS
import path from 'path'

export class BalanceLogger {
  private filePath: string // Add type annotation for filePath

  /**
   * Constructor
   * @param {string} filePath - Path to the CSV file
   */
  constructor(filePath: string = 'balance_log.csv') {
    // Add type annotation for filePath parameter
    this.filePath = path.resolve(filePath)

    // Initialize the CSV file with headers if it doesn't exist
    if (!fs.existsSync(this.filePath)) {
      fs.writeFileSync(this.filePath, 'Timestamp,Balance\n', 'utf8')
    }
  }

  /**
   * Logs balance data to the CSV file
   * @param {number} balance - The current balance to log
   */
  logBalance(balance: number): void {
    // Add type annotation for balance parameter and return type void
    const timestamp: string = new Date().toISOString() // Add type annotation for timestamp
    const data: string = `${timestamp},${balance}\n` // Add type annotation for data

    // Append the data to the file
    fs.appendFileSync(this.filePath, data, 'utf8')
    console.log(`💰 Logged balance: ${balance} at ${timestamp}`)
  }

  /**
   * Reads the log file and returns its content
   * @returns {string} - Content of the CSV file
   */
  readLog(): string {
    // Add return type annotation string
    if (fs.existsSync(this.filePath)) {
      return fs.readFileSync(this.filePath, 'utf8')
    } else {
      throw new Error('Log file does not exist.')
    }
  }
}
