// Đường dẫn tới file CSV
const csvFilePath = './back_test_log.csv';

// Hàm đọc file CSV và chuyển dữ liệu thành JSON
async function fetchCSV(filePath) {
	const response = await fetch(filePath);
	const csvData = await response.text();
	const rows = csvData.trim().split('\n');
	const headers = rows.shift().split(',');

	return rows.map(row => {
		const values = row.split(',');
		return headers.reduce((acc, header, index) => {
			acc[header] = index === 1 ? parseFloat(values[index]) : values[index]; // Chuyển balance thành số
			return acc;
		}, {});
	});
}

// Hàm vẽ biểu đồ 
async function plotChart() {
	const data = await fetchCSV(csvFilePath);

	// Trích xuất timestamp và balance
	const labels = data.map(item => new Date(item.Timestamp).toLocaleTimeString());
	const balances = data.map(item => item.Balance);

	// Lấy canvas
	const ctx = document.getElementById('balanceChart').getContext('2d');


	// Vẽ biểu đồ với zoom và pan
	const chart = new Chart(ctx, {
		type: 'line',
		data: {
			labels: labels,
			datasets: [{
				label: 'Balance',
				data: balances,
				borderColor: 'rgba(75, 192, 192, 1)',
				backgroundColor: 'rgba(75, 192, 192, 0.2)',
				borderWidth: 2,
				tension: 0.4, // Đường cong
			}]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			plugins: {
				legend: {
					display: true,
					position: 'top',
				},
				zoom: {
					pan: {
						enabled: true, // Cho phép pan (kéo)
						mode: 'xy', // Kéo được cả 2 trục X và Y
						threshold: 5, // Ngưỡng cảm biến khi kéo (px)
						rangeMin: {
							x: 0, // Giới hạn min cho trục X
							y: 0  // Giới hạn min cho trục Y
						},
						rangeMax: {
							x: 100, // Giới hạn max cho trục X
							y: 20000 // Giới hạn max cho trục Y
						}
					},
					zoom: {
						wheel: {
							enabled: true, // Cho phép zoom bằng cuộn chuột
						},
						pinch: {
							enabled: true, // Cho phép zoom bằng cử chỉ pinch
						},
						mode: 'xy', // Zoom theo cả trục X và Y
					},
				},
			},
			scales: {
				x: {
					title: {
						display: true,
						text: 'Time',
					}
				},
				y: {
					title: {
						display: true,
						text: 'Balance (USDT)',
					},
					beginAtZero: false,
				}
			}
		}
	});

	// Optional: Reset zoom button logic
	document.getElementById('resetZoom').addEventListener('click', () => {
		chart.resetZoom();
	});
}

// Gọi hàm để vẽ biểu đồ
plotChart();
