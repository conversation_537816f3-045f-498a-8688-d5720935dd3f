<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Full-Screen Balance Chart</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom"></script>
    <style>
        body,
        html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #f4f4f4;
        }

        canvas {
            display: block;
            width: 100vw;
            /* Chiều rộng chiếm toàn bộ màn hình */
            height: 100vh;
            /* Chiều cao chiếm toàn bộ màn hình */
        }
    </style>
</head>

<body>
    <canvas id="balanceChart"></canvas>
    <script src="chart.js"></script>
</body>

</html>