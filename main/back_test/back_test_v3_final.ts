import { CandleChartResult } from 'binance-api-node'
import { generateTradingSignal } from '../../trading_logic_15m_final.ts'
import { loadHistoricalData } from '../utils/trade_utils.ts'

// Constants
const LEVERAGE = 20
const ACCOUNT_PERCENTAGE = 0.95
const INITIAL_BALANCE = 3000

// Backtest function
async function runBacktest(historicalData: CandleChartResult[]): Promise<void> {
  let balance = INITIAL_BALANCE
  let position: {
    side: 'LONG' | 'SHORT'
    entryPrice: number
    quantity: number
    stopLossPrice: number
    takeProfitPrice: number
  } | null = null

  console.log(`Starting backtest with ${historicalData.length} candles`)
  console.log(`Initial balance: ${balance}`)

  // Start from index 99 to have 100 candles available for signal generation
  for (let i = 99; i < historicalData.length; i++) {
    const candle = historicalData[i]
    const currentPrice = parseFloat(candle.close)

    // 1. Check if an open position exists
    if (position) {
      const low = parseFloat(candle.low)
      const high = parseFloat(candle.high)

      // Check for LONG position
      if (position.side === 'LONG') {
        // Check if stop loss was hit
        if (low <= position.stopLossPrice) {
          const profit =
            (position.stopLossPrice - position.entryPrice) * position.quantity * LEVERAGE
          balance += profit
          console.log(
            `Closed LONG at SL ${position.stopLossPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
        }
        // Check if take profit was hit
        else if (high >= position.takeProfitPrice) {
          const profit =
            (position.takeProfitPrice - position.entryPrice) * position.quantity * LEVERAGE
          balance += profit
          console.log(
            `Closed LONG at TP ${position.takeProfitPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
        }
      }
      // Check for SHORT position
      else if (position.side === 'SHORT') {
        // Check if stop loss was hit
        if (high >= position.stopLossPrice) {
          const profit =
            (position.entryPrice - position.stopLossPrice) * position.quantity * LEVERAGE
          balance += profit
          console.log(
            `Closed SHORT at SL ${position.stopLossPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
        }
        // Check if take profit was hit
        else if (low <= position.takeProfitPrice) {
          const profit =
            (position.entryPrice - position.takeProfitPrice) * position.quantity * LEVERAGE
          balance += profit
          console.log(
            `Closed SHORT at TP ${position.takeProfitPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
        }
      }
    }

    // 2. If no position, generate a new signal
    if (!position) {
      const last100Candles = historicalData.slice(i - 99, i + 1)
      const signal = generateTradingSignal(last100Candles)

      if (
        signal.positionSide &&
        signal.entryPrice &&
        signal.stopLossPrice &&
        signal.takeProfitPrice
      ) {
        const orderSize = balance * ACCOUNT_PERCENTAGE
        const positionSize = orderSize * LEVERAGE
        const quantity = positionSize / signal.entryPrice

        console.log(
          `Placed ${signal.positionSide} order at ${signal.entryPrice}, Qty: ${quantity.toFixed(
            6,
          )}`,
        )

        position = {
          side: signal.positionSide,
          entryPrice: signal.entryPrice,
          quantity: quantity,
          stopLossPrice: signal.stopLossPrice,
          takeProfitPrice: signal.takeProfitPrice,
        }
      }
    }
  }

  console.log(`Final Balance: ${balance.toFixed(2)} USDT`)
}

// Run the backtest
const historicalData = loadHistoricalData('./historical_data_mar_15m.csv')
// Use only the first 2000 candles for quicker testing
const limitedData = historicalData.slice(0, 2000)
;(async function() {
  await runBacktest(limitedData)
})()
