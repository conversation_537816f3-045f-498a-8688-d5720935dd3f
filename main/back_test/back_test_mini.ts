import { CandleChartResult } from 'binance-api-node'
import { readFileSync } from 'fs'
import { generateTradingSignal } from '../../trading_logic_15m_final.ts'

// Load a small subset of historical data
function loadHistoricalDataSubset(filePath: string, limit: number = 500): CandleChartResult[] {
  console.log(`Reading file from: ${filePath}`)
  try {
    const csvContent = readFileSync(filePath, 'utf8')
    const lines = csvContent.trim().split('\n')
    const headers = lines[0].split(',')
    console.log(`CSV headers: ${headers.join(', ')}`)
    
    // Take only the first 'limit' lines after the header
    const dataLines = lines.slice(1, limit + 1)
    console.log(`Processing ${dataLines.length} lines out of ${lines.length - 1} total`)

    const result = dataLines.map((line, index) => {
      const [time, open, high, low, close, volume] = line.split(',')
      return {
        openTime: time,
        open,
        high,
        low,
        close,
        volume,
        // Add required properties with default values
        closeTime: 0,
        quoteVolume: '0',
        trades: 0,
        baseAssetVolume: '0',
        quoteAssetVolume: '0',
      } as unknown as CandleChartResult
    })
    console.log(`Processed ${result.length} candles`)
    return result
  } catch (error) {
    console.error(`Error loading historical data: ${error}`)
    throw error
  }
}

// Mini backtest function
async function runMiniBacktest(
  candles: CandleChartResult[],
  initialBalance: number = 3000,
): Promise<void> {
  let balance = initialBalance
  let position: {
    side: 'LONG' | 'SHORT'
    entryPrice: number
    quantity: number
    stopLossPrice: number
    takeProfitPrice: number
  } | null = null

  const leverage = 20
  const accountPercentage = 0.95

  console.log(`Starting mini backtest with ${candles.length} candles`)
  console.log(`Initial balance: ${balance}`)

  // Start from index 99 to have 100 candles available for signal generation
  for (let i = 99; i < candles.length; i++) {
    const candle = candles[i]
    const currentPrice = parseFloat(candle.close)

    // 1. Check if an open position exists
    if (position) {
      const low = parseFloat(candle.low)
      const high = parseFloat(candle.high)

      // Check for LONG position
      if (position.side === 'LONG') {
        // Check if stop loss was hit
        if (low <= position.stopLossPrice) {
          const profit = (position.stopLossPrice - position.entryPrice) * position.quantity * leverage
          balance += profit
          console.log(
            `Closed LONG at SL ${position.stopLossPrice}, Profit: ${profit.toFixed(2)}, Balance: ${balance.toFixed(2)}`
          )
          position = null
        }
        // Check if take profit was hit
        else if (high >= position.takeProfitPrice) {
          const profit = (position.takeProfitPrice - position.entryPrice) * position.quantity * leverage
          balance += profit
          console.log(
            `Closed LONG at TP ${position.takeProfitPrice}, Profit: ${profit.toFixed(2)}, Balance: ${balance.toFixed(2)}`
          )
          position = null
        }
      }
      // Check for SHORT position
      else if (position.side === 'SHORT') {
        // Check if stop loss was hit
        if (high >= position.stopLossPrice) {
          const profit = (position.entryPrice - position.stopLossPrice) * position.quantity * leverage
          balance += profit
          console.log(
            `Closed SHORT at SL ${position.stopLossPrice}, Profit: ${profit.toFixed(2)}, Balance: ${balance.toFixed(2)}`
          )
          position = null
        }
        // Check if take profit was hit
        else if (low <= position.takeProfitPrice) {
          const profit = (position.entryPrice - position.takeProfitPrice) * position.quantity * leverage
          balance += profit
          console.log(
            `Closed SHORT at TP ${position.takeProfitPrice}, Profit: ${profit.toFixed(2)}, Balance: ${balance.toFixed(2)}`
          )
          position = null
        }
      }
    }

    // 2. If no position, generate a new signal
    if (!position) {
      const last100Candles = candles.slice(i - 99, i + 1)
      const signal = generateTradingSignal(last100Candles)

      if (signal.positionSide && signal.entryPrice && signal.stopLossPrice && signal.takeProfitPrice) {
        const orderSize = balance * accountPercentage
        const positionSize = orderSize * leverage
        const quantity = positionSize / signal.entryPrice

        console.log(
          `Placed ${signal.positionSide} order at ${signal.entryPrice}, Qty: ${quantity.toFixed(6)}`
        )

        position = {
          side: signal.positionSide,
          entryPrice: signal.entryPrice,
          quantity: quantity,
          stopLossPrice: signal.stopLossPrice,
          takeProfitPrice: signal.takeProfitPrice
        }
      }
    }
  }

  console.log(`Final Balance: ${balance.toFixed(2)} USDT`)
}

// Run the backtest
;(async () => {
  console.log('Loading historical data subset...')
  try {
    // Load only 500 candles for quick testing
    const historicalData = loadHistoricalDataSubset('./historical_data_mar_15m.csv', 500)
    console.log(`Loaded ${historicalData.length} candles`)
    await runMiniBacktest(historicalData, 3000)
    console.log('Backtest completed.')
  } catch (error) {
    console.error('Error running backtest:', error)
  }
})()
