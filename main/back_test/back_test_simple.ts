import { CandleChartResult } from 'binance-api-node'
import { generateTradingSignal } from '../../trading_logic_15m_v1.ts'
import { loadHistoricalData } from '../utils/trade_utils.ts'

// Simple backtest function
async function runSimpleBacktest(
  candles: CandleChartResult[],
  initialBalance: number = 100,
): Promise<number> {
  let balance = initialBalance
  let position: 'LONG' | 'SHORT' | null = null
  let entryPrice: number | null = null
  let stopLossPrice: number | null = null
  let takeProfitPrice: number | null = null
  let quantity: number | null = null
  const leverage = 20
  const maxAccountPercentage = 0.95

  console.log(`Starting simple backtest with ${candles.length} candles`)
  console.log(`Initial balance: ${balance}`)

  // Process all candles
  const testCandles = candles

  for (let i = 0; i < testCandles.length; i++) {
    const currentCandles = testCandles.slice(0, i + 1)
    const signal = generateTradingSignal(currentCandles)
    const currentPrice = parseFloat(testCandles[i].close)

    // Log every 10 candles
    if (i % 10 === 0) {
      console.log(`Processing candle ${i}, current price: ${currentPrice}`)
    }

    // Open new position
    if (position === null && signal.positionSide !== null && signal.entryPrice !== null) {
      position = signal.positionSide
      entryPrice = signal.entryPrice
      stopLossPrice = signal.stopLossPrice
      takeProfitPrice = signal.takeProfitPrice

      // Calculate position size
      const capitalForTrade = balance * maxAccountPercentage
      const positionValue = capitalForTrade * leverage
      quantity = positionValue / entryPrice

      console.log(`Opened ${position} at ${entryPrice}, Quantity: ${quantity.toFixed(6)}`)
    }
    // Manage existing position
    else if (
      position !== null &&
      entryPrice !== null &&
      stopLossPrice !== null &&
      takeProfitPrice !== null &&
      quantity !== null
    ) {
      // Check for LONG position
      if (position === 'LONG') {
        // Check if price hit stop loss
        if (currentPrice <= stopLossPrice) {
          const profit = (currentPrice - entryPrice) * quantity * leverage
          balance += profit
          console.log(
            `Closed LONG at SL ${currentPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
        // Check if price hit take profit
        else if (currentPrice >= takeProfitPrice) {
          const profit = (takeProfitPrice - entryPrice) * quantity * leverage
          balance += profit
          console.log(
            `Closed LONG at TP ${takeProfitPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
      }
      // Check for SHORT position
      else if (position === 'SHORT') {
        // Check if price hit stop loss
        if (currentPrice >= stopLossPrice) {
          const profit = (entryPrice - currentPrice) * quantity * leverage
          balance += profit
          console.log(
            `Closed SHORT at SL ${currentPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
        // Check if price hit take profit
        else if (currentPrice <= takeProfitPrice) {
          const profit = (entryPrice - takeProfitPrice) * quantity * leverage
          balance += profit
          console.log(
            `Closed SHORT at TP ${takeProfitPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
      }
    }
  }

  console.log(`Final balance: ${balance.toFixed(2)}`)
  return balance
}

// Run the backtest
;(async () => {
  console.log('Loading historical data...')
  try {
    const historicalData = loadHistoricalData('./historical_data_mar_15m.csv')
    console.log(`Loaded ${historicalData.length} candles`)
    const finalBalance = await runSimpleBacktest(historicalData, 100)
    console.log(`Backtest completed. Final balance: ${finalBalance.toFixed(2)}`)
  } catch (error) {
    console.error('Error running backtest:', error)
  }
})()
