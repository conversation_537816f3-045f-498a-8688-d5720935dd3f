import { CandleChartResult } from 'binance-api-node'
import { generateTradingSignal } from '../../trading_logic_optimized.ts'
import { loadHistoricalData } from '../utils/trade_utils.ts'

// Interface for backtest result
interface BacktestResult {
  finalBalance: number
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number
  averageProfit: number
  maxDrawdown: number
}

// Backtest function
async function runBacktest(
  candles: CandleChartResult[],
  initialBalance: number = 1000,
): Promise<BacktestResult> {
  let balance = initialBalance
  let position: 'LONG' | 'SHORT' | null = null
  let entryPrice: number | null = null
  let stopLossPrice: number | null = null
  let takeProfitPrice: number | null = null
  let quantity: number | null = null
  const leverage = 20
  const maxAccountPercentage = 0.95

  let totalTrades = 0
  let winningTrades = 0
  let losingTrades = 0
  let totalProfit = 0
  let maxBalance = balance
  let maxDrawdown = 0

  console.log(`Starting backtest with ${candles.length} candles`)
  console.log(`Initial balance: ${balance}`)

  // Process all candles
  const testCandles = candles

  for (let i = 0; i < testCandles.length; i++) {
    // Log progress every 500 candles
    if (i % 500 === 0) {
      console.log(`Processing candle ${i}/${testCandles.length}`)
    }

    const currentCandles = testCandles.slice(0, i + 1)
    const signal = generateTradingSignal(currentCandles)
    const currentPrice = parseFloat(testCandles[i].close)

    // Open new position
    if (position === null && signal.positionSide !== null && signal.entryPrice !== null) {
      position = signal.positionSide
      entryPrice = signal.entryPrice
      stopLossPrice = signal.stopLossPrice
      takeProfitPrice = signal.takeProfitPrice

      // Calculate position size
      const capitalForTrade = balance * maxAccountPercentage
      const positionValue = capitalForTrade * leverage
      quantity = positionValue / entryPrice

      console.log(
        `[Candle ${i}] Opened ${position} at ${entryPrice}, Quantity: ${quantity.toFixed(6)}`,
      )
    }
    // Manage existing position
    else if (
      position !== null &&
      entryPrice !== null &&
      stopLossPrice !== null &&
      takeProfitPrice !== null &&
      quantity !== null
    ) {
      // Check for LONG position
      if (position === 'LONG') {
        // Check if price hit stop loss
        if (currentPrice <= stopLossPrice) {
          const profit = (currentPrice - entryPrice) * quantity * leverage
          balance += profit
          totalProfit += profit
          totalTrades++
          if (profit > 0) winningTrades++
          else losingTrades++

          console.log(
            `[Candle ${i}] Closed LONG at SL ${currentPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
        // Check if price hit take profit
        else if (currentPrice >= takeProfitPrice) {
          const profit = (takeProfitPrice - entryPrice) * quantity * leverage
          balance += profit
          totalProfit += profit
          totalTrades++
          winningTrades++

          console.log(
            `[Candle ${i}] Closed LONG at TP ${takeProfitPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
      }
      // Check for SHORT position
      else if (position === 'SHORT') {
        // Check if price hit stop loss
        if (currentPrice >= stopLossPrice) {
          const profit = (entryPrice - currentPrice) * quantity * leverage
          balance += profit
          totalProfit += profit
          totalTrades++
          if (profit > 0) winningTrades++
          else losingTrades++

          console.log(
            `[Candle ${i}] Closed SHORT at SL ${currentPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
        // Check if price hit take profit
        else if (currentPrice <= takeProfitPrice) {
          const profit = (entryPrice - takeProfitPrice) * quantity * leverage
          balance += profit
          totalProfit += profit
          totalTrades++
          winningTrades++

          console.log(
            `[Candle ${i}] Closed SHORT at TP ${takeProfitPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
      }
    }

    // Calculate drawdown
    maxBalance = Math.max(maxBalance, balance)
    const drawdown = ((maxBalance - balance) / maxBalance) * 100
    maxDrawdown = Math.max(maxDrawdown, drawdown)
  }

  const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0
  const averageProfit = totalTrades > 0 ? totalProfit / totalTrades : 0

  return {
    finalBalance: balance,
    totalTrades,
    winningTrades,
    losingTrades,
    winRate,
    averageProfit,
    maxDrawdown,
  }
}

// Run the backtest
;(async () => {
  console.log('Loading historical data...')
  try {
    const historicalData = loadHistoricalData('./historical_data_mar_15m.csv')
    console.log(`Loaded ${historicalData.length} candles`)
    const result = await runBacktest(historicalData, 1000)

    console.log('\n=== Backtest Results ===')
    console.log(`Final Balance: ${result.finalBalance.toFixed(2)} USDT`)
    console.log(`Total Trades: ${result.totalTrades}`)
    console.log(`Winning Trades: ${result.winningTrades}`)
    console.log(`Losing Trades: ${result.losingTrades}`)
    console.log(`Win Rate: ${result.winRate.toFixed(2)}%`)
    console.log(`Average Profit: ${result.averageProfit.toFixed(2)} USDT`)
    console.log(`Max Drawdown: ${result.maxDrawdown.toFixed(2)}%`)
  } catch (error) {
    console.error('Error running backtest:', error)
  }
})()
