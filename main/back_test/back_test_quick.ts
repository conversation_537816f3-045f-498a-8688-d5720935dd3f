import { CandleChartResult } from 'binance-api-node'
import { readFileSync } from 'fs'
import { generateTradingSignal } from '../../trading_logic_optimized.ts'

// Load a subset of historical data
function loadHistoricalDataSubset(filePath: string, limit: number = 1000): CandleChartResult[] {
  console.log(`Reading file from: ${filePath}`)
  try {
    const csvContent = readFileSync(filePath, 'utf8')
    const lines = csvContent.trim().split('\n')
    const headers = lines[0].split(',')
    console.log(`CSV headers: ${headers.join(', ')}`)
    
    // Take only the first 'limit' lines after the header
    const dataLines = lines.slice(1, limit + 1)
    console.log(`Processing ${dataLines.length} lines out of ${lines.length - 1} total`)

    const result = dataLines.map((line, index) => {
      const [time, open, high, low, close, volume] = line.split(',')
      if (index < 3) {
        console.log(
          `Sample data [${index}]: time=${time}, open=${open}, high=${high}, low=${low}, close=${close}, volume=${volume}`,
        )
      }
      return {
        openTime: time,
        open,
        high,
        low,
        close,
        volume,
        // Add required properties with default values
        closeTime: 0,
        quoteVolume: '0',
        trades: 0,
        baseAssetVolume: '0',
        quoteAssetVolume: '0',
      } as unknown as CandleChartResult
    })
    console.log(`Processed ${result.length} candles`)
    return result
  } catch (error) {
    console.error(`Error loading historical data: ${error}`)
    throw error
  }
}

// Quick backtest function
async function runQuickBacktest(
  candles: CandleChartResult[],
  initialBalance: number = 1000,
): Promise<number> {
  let balance = initialBalance
  let position: 'LONG' | 'SHORT' | null = null
  let entryPrice: number | null = null
  let stopLossPrice: number | null = null
  let takeProfitPrice: number | null = null
  let quantity: number | null = null
  const leverage = 20
  const maxAccountPercentage = 0.95

  console.log(`Starting quick backtest with ${candles.length} candles`)
  console.log(`Initial balance: ${balance}`)

  for (let i = 0; i < candles.length; i++) {
    // Log progress every 100 candles
    if (i % 100 === 0) {
      console.log(`Processing candle ${i}/${candles.length}`)
    }
    
    const currentCandles = candles.slice(0, i + 1)
    const signal = generateTradingSignal(currentCandles)
    const currentPrice = parseFloat(candles[i].close)

    // Open new position
    if (position === null && signal.positionSide !== null && signal.entryPrice !== null) {
      position = signal.positionSide
      entryPrice = signal.entryPrice
      stopLossPrice = signal.stopLossPrice
      takeProfitPrice = signal.takeProfitPrice

      // Calculate position size
      const capitalForTrade = balance * maxAccountPercentage
      const positionValue = capitalForTrade * leverage
      quantity = positionValue / entryPrice

      console.log(
        `[Candle ${i}] Opened ${position} at ${entryPrice}, Quantity: ${quantity.toFixed(6)}`
      )
    }
    // Manage existing position
    else if (
      position !== null &&
      entryPrice !== null &&
      stopLossPrice !== null &&
      takeProfitPrice !== null &&
      quantity !== null
    ) {
      // Check for LONG position
      if (position === 'LONG') {
        // Check if price hit stop loss
        if (currentPrice <= stopLossPrice) {
          const profit = (currentPrice - entryPrice) * quantity * leverage
          balance += profit

          console.log(
            `[Candle ${i}] Closed LONG at SL ${currentPrice}, Profit: ${profit.toFixed(2)}, Balance: ${balance.toFixed(2)}`
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
        // Check if price hit take profit
        else if (currentPrice >= takeProfitPrice) {
          const profit = (takeProfitPrice - entryPrice) * quantity * leverage
          balance += profit

          console.log(
            `[Candle ${i}] Closed LONG at TP ${takeProfitPrice}, Profit: ${profit.toFixed(2)}, Balance: ${balance.toFixed(2)}`
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
      }
      // Check for SHORT position
      else if (position === 'SHORT') {
        // Check if price hit stop loss
        if (currentPrice >= stopLossPrice) {
          const profit = (entryPrice - currentPrice) * quantity * leverage
          balance += profit

          console.log(
            `[Candle ${i}] Closed SHORT at SL ${currentPrice}, Profit: ${profit.toFixed(2)}, Balance: ${balance.toFixed(2)}`
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
        // Check if price hit take profit
        else if (currentPrice <= takeProfitPrice) {
          const profit = (entryPrice - takeProfitPrice) * quantity * leverage
          balance += profit

          console.log(
            `[Candle ${i}] Closed SHORT at TP ${takeProfitPrice}, Profit: ${profit.toFixed(2)}, Balance: ${balance.toFixed(2)}`
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
      }
    }
  }

  console.log(`Final balance: ${balance.toFixed(2)}`)
  return balance
}

// Run the backtest
;(async () => {
  console.log('Loading historical data subset...')
  try {
    // Load only 2000 candles for quick testing
    const historicalData = loadHistoricalDataSubset('./historical_data_mar_15m.csv', 2000)
    console.log(`Loaded ${historicalData.length} candles`)
    const finalBalance = await runQuickBacktest(historicalData, 1000)
    console.log(`Backtest completed. Final balance: ${finalBalance.toFixed(2)}`)
  } catch (error) {
    console.error('Error running backtest:', error)
  }
})()
