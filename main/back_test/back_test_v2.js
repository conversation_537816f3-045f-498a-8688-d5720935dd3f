// @ts-nocheck

const fs = require('fs')
const csv = require('csv-parser')
const BalanceLogger = require('../log/balance_logger')

const logger = new BalanceLogger('back_test_v2_log.csv')

// --- Configuration ---
const SYMBOL = 'BTCUSDT'
const LEVERAGE = 20
const ACCOUNT_PERCENTAGE = 0.2
const STOP_LOSS_PERCENT = 0.04 // 2%
const TAKE_PROFIT_PERCENT = 0.1 // 5%
const RISK_REWARD_RATIO = 1.5 // R:R = 1:1.5
const INTERVAL = '1m'

const emaPeriod = 20 // Period for EMA
const bbPeriod = 20 // Period for Bollinger Bands
const bbStdDevUp = 2 // Standard deviation multiplier for upper outer BB
const bbStdDevDown = 2 // Standard deviation multiplier for lower outer BB
const bbStdDevUpInner = 1 // Standard deviation multiplier for upper inner BB
const bbStdDevDownInner = 1 // Standard deviation multiplier for lower inner BB

let positionOpen = false
let currentPosition = null
let positionSide = 'NONE'
let inTrade = false
let balance = 1000 // Initial balance for backtesting
let tradeHistory = []

let historicalData = []

// --- Helper Functions ---

function calculateEMA(data, period) {
  if (data.length < period) return null
  let ema = []
  let multiplier = 2 / (period + 1)
  ema[period - 1] = data.slice(0, period).reduce((sum, val) => sum + val, 0) / period // SMA for initial EMA value

  for (let i = period; i < data.length; i++) {
    ema[i] = (data[i] - ema[i - 1]) * multiplier + ema[i - 1]
  }
  return ema.slice(period - 1) // Return EMA values starting from where it's valid
}

function calculateBollingerBands(data, period, stdDevUp, stdDevDown) {
  if (data.length < period) return null
  let bb = { upper: [], lower: [], middle: [] }
  for (let i = period - 1; i < data.length; i++) {
    const slice = data.slice(i - period + 1, i + 1)
    const avg = slice.reduce((sum, val) => sum + val, 0) / period
    const stdDev = Math.sqrt(slice.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / period)

    // @ts-ignore
    bb.middle[i] = avg
    // @ts-ignore
    bb.upper[i] = avg + stdDev * stdDevUp
    // @ts-ignore
    bb.lower[i] = avg - stdDev * stdDevDown
  }
  return {
    upper: bb.upper.slice(period - 1),
    lower: bb.lower.slice(period - 1),
    middle: bb.middle.slice(period - 1),
  }
}

// --- Backtesting Trading Logic Function ---
function backtestTradingLogic(candles) {
  if (candles.length <= Math.max(emaPeriod, bbPeriod)) {
    console.log('Not enough candle data to calculate indicators.')
    return 'NONE'
  }

  const closePrices = candles.map(candle => parseFloat(candle.close))

  // Calculate EMA
  const emaValues = calculateEMA(closePrices, emaPeriod)
  if (!emaValues) {
    console.log('Could not calculate EMA.')
    return 'NONE'
  }
  const currentEMA = emaValues[emaValues.length - 1]

  // Calculate Bollinger Bands (Outer and Inner)
  const outerBB = calculateBollingerBands(closePrices, bbPeriod, bbStdDevUp, bbStdDevDown)
  const innerBB = calculateBollingerBands(closePrices, bbPeriod, bbStdDevUpInner, bbStdDevDownInner)

  if (!outerBB || !innerBB) {
    console.log('Could not calculate Bollinger Bands.')
    return 'NONE'
  }

  const currentClose = closePrices[closePrices.length - 1]
  const previousClose = closePrices[closePrices.length - 2] // For crossover detection

  const currentUpperOuterBB = outerBB.upper[outerBB.upper.length - 1]
  const currentLowerOuterBB = outerBB.lower[outerBB.lower.length - 1]
  const currentUpperInnerBB = innerBB.upper[innerBB.upper.length - 1]
  const currentLowerInnerBB = innerBB.lower[innerBB.lower.length - 1]

  let signal = 'NONE'

  if (inTrade) {
    if (positionSide === 'LONG') {
      if (currentClose < currentEMA) {
        console.log('Exit LONG signal - Price below EMA.')
        signal = 'CLOSE_LONG'
        positionSide = 'NONE'
        inTrade = false
      }
    } else if (positionSide === 'SHORT') {
      if (currentClose > currentEMA) {
        console.log('Exit SHORT signal - Price above EMA.')
        signal = 'CLOSE_SHORT'
        positionSide = 'NONE'
        inTrade = false
      }
    }
    return signal
  }

  if (!inTrade) {
    if (
      currentClose > currentEMA &&
      currentClose > currentUpperInnerBB &&
      previousClose <= currentUpperInnerBB &&
      currentClose < currentUpperOuterBB
    ) {
      console.log('LONG Entry Signal - Price above EMA and Inner Upper BB.')
      signal = 'LONG'
      positionSide = 'LONG'
    } else if (
      currentClose < currentEMA &&
      currentClose < currentLowerInnerBB &&
      previousClose >= currentLowerInnerBB &&
      currentClose > currentLowerOuterBB
    ) {
      console.log('SHORT Entry Signal - Price below EMA and Inner Lower BB.')
      signal = 'SHORT'
      positionSide = 'SHORT'
    }
  }

  return signal
}

async function loadHistoricalData(filePath) {
  return new Promise((resolve, reject) => {
    const data = []
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', row => {
        data.push({
          time: row.time,
          open: parseFloat(row.open),
          high: parseFloat(row.high),
          low: parseFloat(row.low),
          close: parseFloat(row.close),
          volume: parseFloat(row.volume),
        })
      })
      .on('end', () => resolve(data))
      .on('error', reject)
  })
}

async function runBacktest() {
  historicalData = await loadHistoricalData('historical_data.csv')
  console.log('CSV data successfully processed')

  let currentCandles = []
  let openPrice = 0
  let quantity = 0

  for (let i = 0; i < historicalData.length; i++) {
    const candle = historicalData[i]
    currentCandles.push(candle)

    if (currentCandles.length > Math.max(emaPeriod, bbPeriod) + 5) {
      currentCandles.shift() // Keep only the last needed candles for indicator calculation
    }

    const signal = backtestTradingLogic(currentCandles)

    if (!inTrade && (signal === 'LONG' || signal === 'SHORT')) {
      const orderSize = balance * ACCOUNT_PERCENTAGE
      openPrice = parseFloat(candle.close)
      quantity = parseFloat(((orderSize * LEVERAGE) / openPrice).toFixed(3))
      if (signal === 'LONG') {
        console.log(`[${candle.time}] Open LONG at ${openPrice}, quantity: ${quantity}`)
        balance -= 0 // In real backtest you would subtract fees
      } else if (signal === 'SHORT') {
        console.log(`[${candle.time}] Open SHORT at ${openPrice}, quantity: ${quantity}`)
        balance -= 0 // In real backtest you would subtract fees
      }
      inTrade = true
    } else if (inTrade) {
      inTrade = false
      if (positionSide === 'LONG') {
        if (
          signal === 'CLOSE_LONG' ||
          parseFloat(candle.close) < openPrice * (1 - STOP_LOSS_PERCENT) ||
          parseFloat(candle.close) > openPrice * (1 + TAKE_PROFIT_PERCENT)
        ) {
          const closePrice = parseFloat(candle.close)
          const profit = (closePrice - openPrice) * quantity * LEVERAGE
          balance += profit
          console.log(
            `[${candle.time}] Close LONG at ${closePrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
        }
      } else if (positionSide === 'SHORT') {
        if (
          signal === 'CLOSE_SHORT' ||
          parseFloat(candle.close) > openPrice * (1 + STOP_LOSS_PERCENT) ||
          parseFloat(candle.close) < openPrice * (1 - TAKE_PROFIT_PERCENT)
        ) {
          const closePrice = parseFloat(candle.close)
          const profit = (openPrice - closePrice) * quantity * LEVERAGE
          balance += profit
          console.log(
            `[${candle.time}] Close SHORT at ${closePrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
        }
      }
    }
    logger.logBalance(parseFloat(balance.toFixed(2)))
  }
}

runBacktest()
