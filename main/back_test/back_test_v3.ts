import { CandleChartResult } from 'binance-api-node'
import { generateTradingSignal } from '../../trading_logic_15m_final.ts'
import { TradingSignal } from '../utils/trade_model.ts'
import { loadHistoricalData } from '../utils/trade_utils.ts'

// Constants from the bot
const SYMBOL = 'BTCUSDT'
const LEVERAGE = 20
const ACCOUNT_PERCENTAGE = 1
let numberOrder = 0

// Backtest function
async function backtest(
  historicalData: CandleChartResult[],
  initialBalance: number,
): Promise<void> {
  let balance = initialBalance
  let position: {
    side: 'LONG' | 'SHORT'
    entryPrice: number
    quantity: number
    stopLossPrice: number
    takeProfitPrice: number
  } | null = null
  let pendingLimitOrder: {
    side: 'LONG' | 'SHORT'
    price: number
    quantity: number
    stopLossPrice: number
    takeProfitPrice: number
  } | null = null

  // Start from index 99 to have 100 candles available
  for (let i = 99; i < historicalData.length; i++) {
    const candle = historicalData[i]

    // 1. Check if an open position exists
    if (position) {
      const low = parseFloat(candle.low)
      const high = parseFloat(candle.high)

      if (position.side === 'LONG') {
        if (low <= position.stopLossPrice) {
          const exitPrice = position.stopLossPrice
          const pnl = (exitPrice - position.entryPrice) * position.quantity
          balance += pnl
          console.log(
            `💢 Closed LONG at SL: ${exitPrice.toFixed(2)}, PnL: ${pnl.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
        } else if (high >= position.takeProfitPrice) {
          const exitPrice = position.takeProfitPrice
          const pnl = (exitPrice - position.entryPrice) * position.quantity
          balance += pnl
          console.log(
            `💢 Closed LONG at TP: ${exitPrice.toFixed(2)}, PnL: ${pnl.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
        }
      } else if (position.side === 'SHORT') {
        if (high >= position.stopLossPrice) {
          const exitPrice = position.stopLossPrice
          const pnl = (position.entryPrice - exitPrice) * position.quantity
          balance += pnl
          console.log(
            `💢 Closed SHORT at SL: ${exitPrice.toFixed(2)}, PnL: ${pnl.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
        } else if (low <= position.takeProfitPrice) {
          const exitPrice = position.takeProfitPrice
          const pnl = (position.entryPrice - exitPrice) * position.quantity
          balance += pnl
          console.log(
            `💢 Closed SHORT at TP: ${exitPrice.toFixed(2)}, PnL: ${pnl.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
        }
      }
    }

    // 2. If no position, generate a new signal and place orders
    if (!position) {
      const last100Candles = historicalData.slice(i - 99, i + 1)
      const signal: TradingSignal = await generateTradingSignal(last100Candles)

      if (signal.positionSide && signal.entryPrice) {
        const orderSize = balance * ACCOUNT_PERCENTAGE
        const quantity = (orderSize * LEVERAGE) / signal.entryPrice
        pendingLimitOrder = {
          side: signal.positionSide,
          price: parseFloat(signal.entryPrice.toFixed(2)),
          quantity: parseFloat(quantity.toFixed(3)),
          stopLossPrice: signal.stopLossPrice!,
          takeProfitPrice: signal.takeProfitPrice!,
        }
        numberOrder++
        console.log(
          `Placed limit order: ${pendingLimitOrder.side} at ${pendingLimitOrder.price}, Qty: ${pendingLimitOrder.quantity}`,
        )
      } else {
        pendingLimitOrder = null
      }
    }

    // 3. Check if pending limit order fills
    if (pendingLimitOrder) {
      const low = parseFloat(candle.low)
      const high = parseFloat(candle.high)

      if (
        (pendingLimitOrder.side === 'LONG' && low <= pendingLimitOrder.price) ||
        (pendingLimitOrder.side === 'SHORT' && high >= pendingLimitOrder.price)
      ) {
        position = {
          side: pendingLimitOrder.side,
          entryPrice: pendingLimitOrder.price,
          quantity: pendingLimitOrder.quantity,
          stopLossPrice: pendingLimitOrder.stopLossPrice,
          takeProfitPrice: pendingLimitOrder.takeProfitPrice,
        }
        console.log(`Filled ${position.side} at ${position.entryPrice}, Qty: ${position.quantity}`)
        pendingLimitOrder = null
      }
    }
  }

  console.log(`Final Balance: ${balance.toFixed(2)} USDT`)
  console.log(`Total order: ${numberOrder}`)
}

// Run the backtest
const historicalData = loadHistoricalData('./historical_data_mar_15m.csv')
;(async function() {
  backtest(historicalData, 38)
})()
