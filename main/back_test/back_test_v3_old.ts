import csv from 'csv-parser'
import fs from 'fs' // Use import * as fs for CommonJS modules in TS
import { BalanceLogger } from '../log/balance_logger.ts'

const logger = new BalanceLogger('back_test_v3_log.csv')

// --- Configuration ---
const SYMBOL = 'BTCUSDT'
const LEVERAGE = 20
const ACCOUNT_PERCENTAGE = 0.2
const STOP_LOSS_PERCENT = 0.04 // 2%
const TAKE_PROFIT_PERCENT = 0.1 // 5%
const RISK_REWARD_RATIO = 1.5 // R:R = 1:1.5
const INTERVAL = '1m'

const emaPeriod = 20 // Period for EMA
const bbPeriod = 20 // Period for Bollinger Bands
const bbStdDevUp = 2 // Standard deviation multiplier for upper outer BB
const bbStdDevDown = 2 // Standard deviation multiplier for lower outer BB
const bbStdDevUpInner = 1 // Standard deviation multiplier for upper inner BB
const bbStdDevDownInner = 1 // Standard deviation multiplier for lower inner BB

let positionOpen = false
let currentPosition = null
let positionSide = 'NONE'
let inTrade = false
let balance = 1000 // Initial balance for backtesting
let tradeHistory = []

let historicalData: any = []

// --- Helper Functions ---

interface TradingSignal {
  positionSide: 'LONG' | 'SHORT' | null
  entryPrice: number | null
  takeProfitPrice: number | null
  stopLossPrice: number | null
}

interface CandleChartResult {
  openTime: number
  open: string
  high: string
  low: string
  close: string
  volume: string
  closeTime: number
  quoteVolume: string
  trades: number
  baseAssetVolume: string
  quoteAssetVolume: string
}

// Hàm tính toán kích thước lệnh
async function calculateOrderSize() {
  const usdtBalance = balance
  const orderValue = usdtBalance * ACCOUNT_PERCENTAGE
  console.log('Order Value: ', orderValue)
  return orderValue
}

/**
 * Function to get trend from Middle Bollinger Band values (TypeScript Version Converted)
 * @param {number[]} middleBBValues
 * @returns {'UPTREND' | 'DOWNTREND' | 'SIDEWAYS'}
 */
function getTrendFromMiddleBB(middleBBValues: number[]): 'UPTREND' | 'DOWNTREND' | 'SIDEWAYS' {
  if (!middleBBValues || middleBBValues.length < 2) {
    console.log('Insufficient middle Bollinger Band data to determine trend.')
    return 'SIDEWAYS' // Default to sideways if not enough data
  }

  const lookbackPeriod = Math.min(5, middleBBValues.length - 1) // Look back at most 5 periods, or less if data is shorter

  const currentMiddleBB = middleBBValues[middleBBValues.length - 1]
  const previousMiddleBB = middleBBValues[middleBBValues.length - lookbackPeriod]

  const thresholdPercentage = 0.0005 // 0.05% threshold - adjust as needed for sensitivity
  const thresholdValueUp = previousMiddleBB * (1 + thresholdPercentage)
  const thresholdValueDown = previousMiddleBB * (1 - thresholdPercentage)

  if (currentMiddleBB > thresholdValueUp) {
    return 'UPTREND'
  } else if (currentMiddleBB < thresholdValueDown) {
    return 'DOWNTREND'
  } else {
    return 'SIDEWAYS'
  }
}

/**
 * Function to calculate Bollinger Bands
 * @param {number[]} closePrices
 * @param {number} period
 * @param {number} multiplier
 * @returns {{ middleBB: number[], upperBB: number[], lowerBB: number[] }}
 */
function calculateBollingerBands(
  closePrices: number[],
  period: number = 20,
  multiplier: number = 2,
): { middleBB: number[]; upperBB: number[]; lowerBB: number[] } {
  const middleBB: number[] = []
  const upperBB: number[] = []
  const lowerBB: number[] = []

  if (closePrices.length < period) {
    return { middleBB: [], upperBB: [], lowerBB: [] } // Not enough data
  }

  for (let i = period - 1; i < closePrices.length; i++) {
    const slice = closePrices.slice(i - period + 1, i + 1)
    const sma = slice.reduce((sum, price) => sum + price, 0) / period
    middleBB.push(sma)

    const stdDev = Math.sqrt(
      slice.reduce((sumSqDiff, price) => sumSqDiff + Math.pow(price - sma, 2), 0) / period,
    )
    upperBB.push(sma + multiplier * stdDev)
    lowerBB.push(sma - multiplier * stdDev)
  }
  return { middleBB, upperBB, lowerBB }
}

// --- Backtesting Trading Logic Function ---

async function loadHistoricalData(filePath: string) {
  return new Promise((resolve, reject) => {
    const data: any = []
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', row => {
        data.push({
          time: row.time,
          open: parseFloat(row.open),
          high: parseFloat(row.high),
          low: parseFloat(row.low),
          close: parseFloat(row.close),
          volume: parseFloat(row.volume),
        })
      })
      .on('end', () => resolve(data))
      .on('error', reject)
  })
}

async function generateTradingSignal(candles: CandleChartResult[]): Promise<TradingSignal> {
  const bbPeriod: number = 20,
    bbMultiplier: number = 2

  if (!candles || candles.length < bbPeriod) {
    console.log('Insufficient candle data to generate trading signal.')
    return {
      positionSide: null,
      entryPrice: null,
      takeProfitPrice: null,
      stopLossPrice: null,
    }
  }

  const closePrices = candles.map(candle => parseFloat(candle.close))
  const bbBands = calculateBollingerBands(closePrices, bbPeriod, bbMultiplier)
  const lastCandle = candles[candles.length - 1]
  const previousCandle = candles[candles.length - 2]
  const currentIndexBB = candles.length - bbPeriod // Index for BB arrays (adjust for 0-based index)

  const middleBBValuesForTrend = bbBands.middleBB.slice(
    Math.max(0, currentIndexBB - 5),
    bbBands.middleBB.length,
  )
  const trend = getTrendFromMiddleBB(middleBBValuesForTrend)

  if (
    trend === 'UPTREND' &&
    parseFloat(lastCandle.close) > bbBands.upperBB[bbBands.upperBB.length - 1]
  ) {
    // Long Entry Signal
    const entryPrice = parseFloat(lastCandle.close)
    const stopLossPrice = parseFloat(previousCandle.low) // Example stop loss below entry candle low
    const takeProfitPrice = entryPrice * 1.002 // Example 0.2% Take Profit

    return {
      positionSide: 'LONG',
      entryPrice: entryPrice,
      takeProfitPrice: takeProfitPrice,
      stopLossPrice: stopLossPrice,
    }
  } else if (
    trend === 'DOWNTREND' &&
    parseFloat(lastCandle.close) < bbBands.lowerBB[bbBands.lowerBB.length - 1]
  ) {
    // Short Entry Signal
    const entryPrice = parseFloat(lastCandle.close)
    const stopLossPrice = parseFloat(previousCandle.high) // Example stop loss above entry candle high
    const takeProfitPrice = entryPrice * 0.998 // Example 0.2% Take Profit

    return {
      positionSide: 'SHORT',
      entryPrice: entryPrice,
      takeProfitPrice: takeProfitPrice,
      stopLossPrice: stopLossPrice,
    }
  }

  return { positionSide: null, entryPrice: null, takeProfitPrice: null, stopLossPrice: null } // No signal
}

async function runBacktest() {
  historicalData = await loadHistoricalData('historical_data.csv')
  console.log('CSV data successfully processed')

  let currentCandles = []
  let openPrice = 0
  let quantity = 0

  for (let i = 0; i < historicalData.length; i++) {
    const candle = historicalData[i]
    currentCandles.push(candle)

    if (currentCandles.length > Math.max(emaPeriod, bbPeriod) + 5) {
      currentCandles.shift() // Keep only the last needed candles for indicator calculation
    }

    const signal: TradingSignal = await generateTradingSignal(currentCandles)

    if (!inTrade && (signal.positionSide === 'LONG' || signal.positionSide === 'SHORT')) {
      const orderSize = balance * ACCOUNT_PERCENTAGE
      openPrice = parseFloat(candle.close)
      quantity = parseFloat(((orderSize * LEVERAGE) / openPrice).toFixed(3))
      if (signal.positionSide === 'LONG') {
        console.log(`[${candle.time}] Open LONG at ${openPrice}, quantity: ${quantity}`)
        balance -= 0 // In real backtest you would subtract fees
      } else if (signal.positionSide === 'SHORT') {
        console.log(`[${candle.time}] Open SHORT at ${openPrice}, quantity: ${quantity}`)
        balance -= 0 // In real backtest you would subtract fees
      }
      inTrade = true
    } else if (inTrade) {
      inTrade = false
      if (positionSide === 'LONG') {
        if (
          signal.positionSide === 'CLOSE_LONG' ||
          parseFloat(candle.close) < openPrice * (1 - STOP_LOSS_PERCENT) ||
          parseFloat(candle.close) > openPrice * (1 + TAKE_PROFIT_PERCENT)
        ) {
          const closePrice = parseFloat(candle.close)
          const profit = (closePrice - openPrice) * quantity * LEVERAGE
          balance += profit
          console.log(
            `[${candle.time}] Close LONG at ${closePrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
        }
      } else if (positionSide === 'SHORT') {
        if (
          signal.positionSide === 'CLOSE_SHORT' ||
          parseFloat(candle.close) > openPrice * (1 + STOP_LOSS_PERCENT) ||
          parseFloat(candle.close) < openPrice * (1 - TAKE_PROFIT_PERCENT)
        ) {
          const closePrice = parseFloat(candle.close)
          const profit = (openPrice - closePrice) * quantity * LEVERAGE
          balance += profit
          console.log(
            `[${candle.time}] Close SHORT at ${closePrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
        }
      }
    }
    logger.logBalance(parseFloat(balance.toFixed(2)))
  }
}

runBacktest()
