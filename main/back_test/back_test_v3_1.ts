import { CandleChartResult } from 'binance-api-node'
import { generateTradingSignal } from '../../trading_logic_15m_v1.ts'
import { loadHistoricalData } from '../utils/trade_utils.ts'

// Interface cho kết quả backtest
interface BacktestResult {
  finalBalance: number
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number
  averageProfit: number
  maxDrawdown: number
}
// Hàm chạy backtest
async function runBacktest(
  candles: CandleChartResult[],
  initialBalance: number = 1000,
): Promise<BacktestResult> {
  let balance = initialBalance
  let position: 'LONG' | 'SHORT' | null = null
  let entryPrice: number | null = null
  let stopLossPrice: number | null = null
  let takeProfitPrice: number | null = null
  let quantity: number | null = null
  const leverage = 20 // Đòn bẩy x20
  const maxAccountPercentage = 0.95 // Tối đa 95% tài khoản

  let totalTrades = 0
  let winningTrades = 0
  let losingTrades = 0
  let totalProfit = 0
  let maxBalance = balance
  let maxDrawdown = 0

  for (let i = 0; i < candles.length; i++) {
    const currentCandles = candles.slice(0, i + 1)
    const signal = generateTradingSignal(currentCandles)
    const currentPrice = parseFloat(candles[i].close)

    // Mở vị thế mới
    if (position === null && signal.positionSide !== null && signal.entryPrice !== null) {
      position = signal.positionSide
      entryPrice = signal.entryPrice
      stopLossPrice = signal.stopLossPrice
      takeProfitPrice = signal.takeProfitPrice

      // Tính kích thước vị thế
      const capitalForTrade = balance * maxAccountPercentage // 20% tài khoản
      const positionValue = capitalForTrade * leverage // Giá trị vị thế với đòn bẩy
      quantity = positionValue / entryPrice // Số lượng tài sản

      console.log(
        `[${candles[i].openTime}] Opened ${position} at ${entryPrice}, Quantity: ${quantity.toFixed(
          6,
        )}`,
      )
    }
    // Quản lý vị thế hiện tại
    else if (
      position !== null &&
      entryPrice !== null &&
      stopLossPrice !== null &&
      takeProfitPrice !== null &&
      quantity !== null
    ) {
      // Check for LONG position
      if (position === 'LONG') {
        // Check if price hit stop loss
        if (currentPrice <= stopLossPrice) {
          // Đóng LONG khi chạm stop-loss
          const profit = (currentPrice - entryPrice) * quantity * leverage
          balance += profit
          totalProfit += profit
          totalTrades++
          if (profit > 0) winningTrades++
          else losingTrades++

          console.log(
            `[${candles[i].openTime}] Closed LONG at SL ${currentPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
        // Check if price hit take profit
        else if (currentPrice >= takeProfitPrice) {
          // Đóng LONG khi chạm take-profit
          const profit = (takeProfitPrice - entryPrice) * quantity * leverage
          balance += profit
          totalProfit += profit
          totalTrades++
          winningTrades++

          console.log(
            `[${
              candles[i].openTime
            }] Closed LONG at TP ${takeProfitPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
      }
      // Check for SHORT position
      else if (position === 'SHORT') {
        // Check if price hit stop loss
        if (currentPrice >= stopLossPrice) {
          // Đóng SHORT khi chạm stop-loss
          const profit = (entryPrice - currentPrice) * quantity * leverage
          balance += profit
          totalProfit += profit
          totalTrades++
          if (profit > 0) winningTrades++
          else losingTrades++

          console.log(
            `[${candles[i].openTime}] Closed SHORT at SL ${currentPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
        // Check if price hit take profit
        else if (currentPrice <= takeProfitPrice) {
          // Đóng SHORT khi chạm take-profit
          const profit = (entryPrice - takeProfitPrice) * quantity * leverage
          balance += profit
          totalProfit += profit
          totalTrades++
          winningTrades++

          console.log(
            `[${
              candles[i].openTime
            }] Closed SHORT at TP ${takeProfitPrice}, Profit: ${profit.toFixed(
              2,
            )}, Balance: ${balance.toFixed(2)}`,
          )
          position = null
          entryPrice = null
          stopLossPrice = null
          takeProfitPrice = null
          quantity = null
        }
      }
    }

    // Tính drawdown
    maxBalance = Math.max(maxBalance, balance)
    const drawdown = ((maxBalance - balance) / maxBalance) * 100
    maxDrawdown = Math.max(maxDrawdown, drawdown)
  }

  const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0
  const averageProfit = totalTrades > 0 ? totalProfit / totalTrades : 0

  return {
    finalBalance: balance,
    totalTrades,
    winningTrades,
    losingTrades,
    winRate,
    averageProfit,
    maxDrawdown,
  }
}

// Chạy backtest
;(async () => {
  console.log('Loading historical data...')
  const historicalData: CandleChartResult[] = loadHistoricalData('./historical_data_mar_15m.csv')
  console.log(`Loaded ${historicalData.length} candles`)
  console.log('Starting backtest...')
  const result = await runBacktest(historicalData, 100)
  const finalBalance = result.finalBalance

  console.log('\n=== Kết quả Backtest ===')
  console.log(`Số dư cuối cùng: ${finalBalance.toFixed(2)} USDT`)
  console.log(`Tổng số giao dịch: ${result.totalTrades}`)
  console.log(`Giao dịch thắng: ${result.winningTrades}`)
  console.log(`Giao dịch thua: ${result.losingTrades}`)
  console.log(`Tỷ lệ thắng: ${result.winRate.toFixed(2)}%`)
  console.log(`Lợi nhuận trung bình: ${result.averageProfit.toFixed(2)} USDT`)
  console.log(`Drawdown tối đa: ${result.maxDrawdown.toFixed(2)}%`)
})()
