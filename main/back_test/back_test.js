const fs = require('fs');
const csv = require('csv-parser');
const BalanceLogger = require('../log/balance_logger');
const { log } = require('console');
const logger = new BalanceLogger('back_test_log.csv');

/** Configuration */
const SYMBOL = 'BTCUSDT';
const ACCOUNT_PERCENTAGE = 0.2;
const LEVERAGE = 20;
const STOP_LOSS_PERCENT = 0.04; // 4%
const TAKE_PROFIT_PERCENT = 0.1; // 10%
const RSI_PERIOD = 14;
const BB_PERIOD = 20;
const BB_STD_DEV = 2;

/** Portfolio and Historical Data */
let portfolio = { balance: 1000, position: null }; // Starting balance of 1000 USDT
let historicalData = [];
let currentIndex = 0;

/** Load Historical Data */
async function loadHistoricalData(filePath) {
  return new Promise((resolve, reject) => {
    const data = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (row) => {
        data.push({
          time: row.time,
          open: parseFloat(row.open),
          high: parseFloat(row.high),
          low: parseFloat(row.low),
          close: parseFloat(row.close),
          volume: parseFloat(row.volume),
        });
      })
      .on('end', () => resolve(data))
      .on('error', reject);
  });
}

/** Calculate RSI */
function calculateRSI(prices, period = RSI_PERIOD) {
  if (prices.length < period + 1) {
    throw new Error('Not enough data to calculate RSI');
  }

  let gains = 0;
  let losses = 0;

  for (let i = 1; i <= period; i++) {
    const change = prices[i] - prices[i - 1];
    if (change > 0) gains += change;
    else losses -= change;
  }

  let avgGain = gains / period;
  let avgLoss = losses / period;

  for (let i = period + 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    const gain = change > 0 ? change : 0;
    const loss = change < 0 ? -change : 0;

    avgGain = (avgGain * (period - 1) + gain) / period;
    avgLoss = (avgLoss * (period - 1) + loss) / period;
  }

  const rs = avgGain / avgLoss || 0;
  return 100 - 100 / (1 + rs);
}

/** Calculate Bollinger Bands */
function calculateBollingerBands(data, period = BB_PERIOD, stdDev = BB_STD_DEV) {
  const sma = data.slice(-period).reduce((sum, val) => sum + val, 0) / period;
  const variance = data.slice(-period).reduce((sum, val) => sum + Math.pow(val - sma, 2), 0) / period;
  const std = Math.sqrt(variance);
  return {
    middle: sma,
    upper: sma + stdDev * std,
    lower: sma - stdDev * std,
  };
}

/** Get Next Candle */
function getNextCandle() {
  if (currentIndex < historicalData.length) {
    return historicalData[currentIndex++];
  }
  return null;
}

/** Simulate Trade */
function simulateTrade(signal, price) {
  if (signal === 'LONG' || signal === 'SHORT') {
    const orderSize = portfolio.balance * ACCOUNT_PERCENTAGE;
    const quantity = (orderSize * LEVERAGE) / price;

    portfolio.position = {
      side: signal,
      entryPrice: price,
      quantity,
      leverage: LEVERAGE,
    };
    console.log(`Opened ${signal} position at ${price}`);
  } else if (portfolio.position) {
    const position = portfolio.position;
    const pnl =
      position.side === 'LONG'
        ? (price - position.entryPrice) * position.quantity
        : (position.entryPrice - price) * position.quantity;

    portfolio.balance += pnl;
    console.log(
      `Closed position at ${price}, PnL: ${pnl.toFixed(2)}, Balance: ${portfolio.balance.toFixed(2)}`
    );
    portfolio.position = null;
    logger.logBalance(portfolio.balance);
  }
}

/** Trading Logic */
function tradeLogic(indicators, currentPrice) {
  const { rsi, bb } = indicators;

  if (rsi < 40 && currentPrice < bb.lower) {
    return 'LONG';
  }

  if (rsi > 60 && currentPrice > bb.upper) {
    return 'SHORT';
  }

  return 'NONE';
}

/** Main Backtesting Loop */
async function backtest() {
  console.log('Loading historical data...');
  historicalData = await loadHistoricalData('historical_data.csv');
  console.log('Starting backtest...');

  while (true) {
    const candle = getNextCandle();

    if (!candle) break;

    const dataSlice = historicalData.slice(
      Math.max(0, currentIndex - 100),
      currentIndex
    );

    const prices = dataSlice.map(candle => parseFloat(candle.close));
    let indicators = { rsi: null, bb: null };

    if (prices.length < 14 + 1) { // Adjust based on your RSI period
      console.error('Not enough data to calculate RSI. Skipping this step.');
      continue;
    }

    const rsi = calculateRSI(prices, 14); // Calculate RSI
    const bb = calculateBollingerBands(prices, 20, 2); // Calculate Bollinger Bands


    indicators = { rsi: rsi, bb: bb };

    const signal = tradeLogic(indicators, candle.close);
    simulateTrade(signal, candle.close);
  }

  console.log(`Backtest completed. Final Balance: ${portfolio.balance.toFixed(2)} USDT`);
}

/** Run the Backtest */
backtest().catch((err) => console.error('Error during backtest:', err));
