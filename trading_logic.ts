import { CandleChartResult } from 'binance-api-node'
import { TradingSignal } from './bot_v3'

/**
 * Function to get trend from Middle Bollinger Band values (TypeScript Version Converted)
 * @param {number[]} middleBBValues
 * @returns {'UPTREND' | 'DOWNTREND' | 'SIDEWAYS'}
 */
function getTrendFromMiddleBB(middleBBValues: number[]): 'UPTREND' | 'DOWNTREND' | 'SIDEWAYS' {
  if (!middleBBValues || middleBBValues.length < 2) {
    console.log('Insufficient middle Bollinger Band data to determine trend.')
    return 'SIDEWAYS' // Default to sideways if not enough data
  }

  const lookbackPeriod = Math.min(5, middleBBValues.length - 1) // Look back at most 5 periods, or less if data is shorter

  const currentMiddleBB = middleBBValues[middleBBValues.length - 1]
  const previousMiddleBB = middleBBValues[middleBBValues.length - lookbackPeriod]

  const thresholdPercentage = 0.0005 // 0.05% threshold - adjust as needed for sensitivity
  const thresholdValueUp = previousMiddleBB * (1 + thresholdPercentage)
  const thresholdValueDown = previousMiddleBB * (1 - thresholdPercentage)

  if (currentMiddleBB > thresholdValueUp) {
    return 'UPTREND'
  } else if (currentMiddleBB < thresholdValueDown) {
    return 'DOWNTREND'
  } else {
    return 'SIDEWAYS'
  }
}

/**
 * Function to calculate Bollinger Bands
 * @param {number[]} closePrices
 * @param {number} period
 * @param {number} multiplier
 * @returns {{ middleBB: number[], upperBB: number[], lowerBB: number[] }}
 */
function calculateBollingerBands(
  closePrices: number[],
  period: number = 20,
  multiplier: number = 2,
): { middleBB: number[]; upperBB: number[]; lowerBB: number[] } {
  const middleBB: number[] = []
  const upperBB: number[] = []
  const lowerBB: number[] = []

  if (closePrices.length < period) {
    return { middleBB: [], upperBB: [], lowerBB: [] } // Not enough data
  }

  for (let i = period - 1; i < closePrices.length; i++) {
    const slice = closePrices.slice(i - period + 1, i + 1)
    const sma = slice.reduce((sum, price) => sum + price, 0) / period
    middleBB.push(sma)

    const stdDev = Math.sqrt(
      slice.reduce((sumSqDiff, price) => sumSqDiff + Math.pow(price - sma, 2), 0) / period,
    )
    upperBB.push(sma + multiplier * stdDev)
    lowerBB.push(sma - multiplier * stdDev)
  }
  return { middleBB, upperBB, lowerBB }
}

/**
 * Generates a trading signal based on Bollinger Bands and trend analysis.
 *
 * @async
 * @param {CandleChartResult[]} candles - Array of Binance candle data.
 * @param {number} bbPeriod - Period for Bollinger Band calculation.
 * @param {number} bbMultiplier - Multiplier for Bollinger Band standard deviation.
 * @returns {Promise<TradingSignal>}
 *          Returns an object with trade signal details if a signal is generated, otherwise null.
 */
export async function generateTradingSignal(candles: CandleChartResult[]): Promise<TradingSignal> {
  const bbPeriod: number = 20,
    bbMultiplier: number = 2

  if (!candles || candles.length < bbPeriod) {
    console.log('Insufficient candle data to generate trading signal.')
    return {
      positionSide: null,
      entryPrice: null,
      takeProfitPrice: null,
      stopLossPrice: null,
    }
  }

  const closePrices = candles.map(candle => parseFloat(candle.close))
  const bbBands = calculateBollingerBands(closePrices, bbPeriod, bbMultiplier)
  const lastCandle = candles[candles.length - 1]
  const previousCandle = candles[candles.length - 2]
  const currentIndexBB = candles.length - bbPeriod // Index for BB arrays (adjust for 0-based index)

  const middleBBValuesForTrend = bbBands.middleBB.slice(
    Math.max(0, currentIndexBB - 5),
    bbBands.middleBB.length,
  )
  const trend = getTrendFromMiddleBB(middleBBValuesForTrend)

  if (
    trend === 'UPTREND' &&
    parseFloat(lastCandle.close) > bbBands.upperBB[bbBands.upperBB.length - 1]
  ) {
    // Long Entry Signal
    const entryPrice = parseFloat(lastCandle.close)
    const stopLossPrice = parseFloat(previousCandle.low) // Example stop loss below entry candle low
    const takeProfitPrice = entryPrice * 1.002 // Example 0.2% Take Profit

    return {
      positionSide: 'LONG',
      entryPrice: entryPrice,
      takeProfitPrice: takeProfitPrice,
      stopLossPrice: stopLossPrice,
    }
  } else if (
    trend === 'DOWNTREND' &&
    parseFloat(lastCandle.close) < bbBands.lowerBB[bbBands.lowerBB.length - 1]
  ) {
    // Short Entry Signal
    const entryPrice = parseFloat(lastCandle.close)
    const stopLossPrice = parseFloat(previousCandle.high) // Example stop loss above entry candle high
    const takeProfitPrice = entryPrice * 0.998 // Example 0.2% Take Profit

    return {
      positionSide: 'SHORT',
      entryPrice: entryPrice,
      takeProfitPrice: takeProfitPrice,
      stopLossPrice: stopLossPrice,
    }
  }

  return { positionSide: null, entryPrice: null, takeProfitPrice: null, stopLossPrice: null } // No signal
}
